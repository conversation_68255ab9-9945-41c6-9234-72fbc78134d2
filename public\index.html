<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Debug Demo</title>
</head>
<body>
  <canvas id="main" width="640" height="480"></canvas>
  <script type="module">
    import './src/debug/hotkeys.js';
    import { logDebug } from './src/debug/logger.js';
    logDebug('Debug system initialized');
  </script>
  <div id="game-debug-console" style="
  position: fixed; bottom: 0; left: 0; width: 100%; height: 120px;
  background: #000; color: #0f0; overflow-y: auto;
  font: 12px monospace; padding: 4px; z-index: 9999;">
  </div>
</body>
</html>
