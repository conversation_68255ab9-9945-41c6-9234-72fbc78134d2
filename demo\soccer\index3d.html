<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Football Sim 3D - Enhanced</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Main overlay for game info */
        #overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: sans-serif;
            z-index: 100;
        }

        #hud {
            margin-top: 4px;
            font-size: 18px;
            font-weight: bold;
        }

        /* Scoreboard styling */
        #scoreboard {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            z-index: 100;
            border: 2px solid #fff;
        }

        /* Control panel button */
        #control-panel-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: 2px solid #fff;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            z-index: 1000;
        }

        #control-panel-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Control panel */
        #control-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 380px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 20px;
            box-sizing: border-box;
            transition: right 0.3s ease;
            z-index: 999;
            overflow-y: auto;
            border-left: 2px solid #fff;
        }

        #control-panel.open {
            right: 0;
        }

        #control-panel h3 {
            margin-top: 0;
            color: #fff;
            border-bottom: 1px solid #666;
            padding-bottom: 10px;
        }

        #control-panel label {
            display: block;
            margin: 10px 0;
            cursor: pointer;
        }

        #control-panel input[type="checkbox"] {
            margin-right: 8px;
        }

        #control-panel select {
            width: 100%;
            padding: 5px;
            margin: 5px 0;
            background: #333;
            color: white;
            border: 1px solid #666;
            border-radius: 3px;
        }

        /* Commentary box */
        #commentary {
            position: absolute;
            bottom: 20px;
            left: 20px;
            width: 400px;
            height: 150px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #fff;
            overflow-y: auto;
            font-size: 14px;
            z-index: 100;
        }

        /* Wear test mode indicator */
        #wearTestIndicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            z-index: 200;
            display: none;
            border: 2px solid #4CAF50;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        #wearTestIndicator.active {
            display: block;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        #commentary h4 {
            margin: 0 0 10px 0;
            color: #fff;
        }

        #commentary-content {
            height: 100px;
            overflow-y: auto;
        }

        /* Weather controls */
        #weather-controls {
            position: absolute;
            top: 80px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #666;
            z-index: 100;
        }

        /* Match info */
        #match-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid #666;
            font-size: 14px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <!-- Main game overlay -->
    <div id="overlay">
        <div id="info">Use WASD to move, Space to kick, Mouse to look around</div>
    </div>

    <!-- Scoreboard -->
    <div id="scoreboard">
        <div id="score">Home 0 : 0 Away</div>
        <div id="timer" style="font-size: 16px; margin-top: 5px;">00:00 - 1st Half</div>
    </div>

    <!-- Match info -->
    <div id="match-info">
        <div>Ball: <span id="ball-owner">Loose</span></div>
        <div>Weather: <span id="weather-display">Clear</span></div>
        <div>Phase: <span id="game-phase">Kickoff</span></div>
    </div>

    <!-- Quick Access Panel -->
    <div id="quickAccess" style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.9); color: white; padding: 10px; border-radius: 8px; font-family: Arial, sans-serif; z-index: 1001;">
        <div style="font-weight: bold; margin-bottom: 10px;">🎮 Quick Controls</div>
        <button id="toggleControlMode" style="margin: 2px; padding: 5px 10px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">🎮 Player Mode</button>
        <br>
        <button id="toggleCameraPanel" style="margin: 2px; padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">📹 Camera</button>
        <button id="toggleGrassPanel" style="margin: 2px; padding: 5px 10px; background: #8BC34A; color: white; border: none; border-radius: 4px; cursor: pointer;">🌱 Grass</button>
        <button id="toggleStadiumPanel" style="margin: 2px; padding: 5px 10px; background: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer;">🏟️ Stadium</button>
        <button id="toggleDebugPanel" style="margin: 2px; padding: 5px 10px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">🔧 Debug</button>
        <button id="toggleGamePanel" style="margin: 2px; padding: 5px 10px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">⚽ Game</button>
    </div>

    <!-- Wear test mode indicator -->
    <div id="wearTestIndicator">
        🌱 GRASS WEAR TEST MODE ACTIVE 🌱<br>
        <div style="font-size: 14px; margin-top: 5px;">Move mouse over field to simulate wear</div>
    </div>

    <!-- Weather controls -->
    <div id="weather-controls">
        <label>Weather:</label>
        <select id="weatherSelect">
            <option value="clear">Clear</option>
            <option value="rain">Rain</option>
            <option value="wind">Wind</option>
        </select>
    </div>

    <!-- Control panel toggle -->
    <button id="control-panel-toggle">≡</button>

    <!-- Control panel -->
    <div id="control-panel">
        <h3>🎮 Game Controls</h3>

        <details open>
            <summary>🔧 Debug Options</summary>
            <div style="margin: 10px 0;">
                <label>
                    <input type="checkbox" id="debug-zones">
                    <span style="font-weight: bold;">Player Zones</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Show tactical positioning zones (selected player only)</div>
                </label>
                <label>
                    <input type="checkbox" id="debug-fov">
                    <span style="font-weight: bold;">Field of View</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Show player perception cones (selected player only)</div>
                </label>
                <label>
                    <input type="checkbox" id="debug-ball">
                    <span style="font-weight: bold;">Ball Physics</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Show ball trajectory and vectors</div>
                </label>
                <label>
                    <input type="checkbox" id="debug-formation">
                    <span style="font-weight: bold;">Formation Debug</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Show formation positioning guides (selected player only)</div>
                </label>
                <label>
                    <input type="checkbox" id="debug-targets">
                    <span style="font-weight: bold;">Player Targets</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Show movement target indicators (selected player only)</div>
                </label>
            </div>
        </details>

        <details>
            <summary>⚽ Game Settings</summary>
            <div style="margin: 10px 0;">
                <label>Game Speed:</label>
                <select id="gameSpeed">
                    <option value="0.5">0.5x</option>
                    <option value="1" selected>1x</option>
                    <option value="1.5">1.5x</option>
                    <option value="2">2x</option>
                </select>

                <label style="margin-top: 10px;">
                    <input type="checkbox" id="pauseGame"> Pause Game
                </label>
            </div>
        </details>

        <details>
            <summary>🌱 Grass Wear Test Mode</summary>
            <div style="margin: 10px 0;">
                <label style="margin-bottom: 10px;">
                    <input type="checkbox" id="wearTestMode">
                    <span style="font-weight: bold; color: #4CAF50;">Enable Wear Test Mode</span>
                    <div style="font-size: 11px; color: #aaa; margin-left: 20px;">Move mouse over field to simulate player wear</div>
                </label>

                <label>Wear Intensity: <span id="wearIntensityValue">2.0</span></label>
                <input type="range" id="wearIntensity" min="0.5" max="10" step="0.5" value="2.0" style="width: 100%;">

                <label>Activity Type:</label>
                <select id="wearActivityType" style="margin-bottom: 10px;">
                    <option value="walk">🚶 Walk (Light)</option>
                    <option value="run">🏃 Run (Moderate)</option>
                    <option value="sprint">💨 Sprint (Fast)</option>
                    <option value="tackle" selected>🤼 Tackle (Heavy)</option>
                    <option value="slide">🛝 Slide (Maximum)</option>
                    <option value="shot">⚽ Shot (Divot)</option>
                    <option value="foul">💥 Foul (Scuff)</option>
                    <option value="jump">🦘 Jump (Impact)</option>
                </select>

                <button id="resetWear" style="width: 100%; padding: 8px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer; margin-top: 5px;">
                    🔄 Reset All Wear
                </button>



                    <button id="forceAllChunks" style="width: 100%; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px;">
                        🌾 Ensure All Grass Visible
                    </button>
                </div>
            </div>
        </details>

        <details>
            <summary>🎥 Camera Settings</summary>
            <div style="margin: 10px 0;">
                <label>Camera Mode:</label>
                <select id="cameraMode">
                    <option value="follow">Follow Player</option>
                    <option value="broadcast">TV Broadcast</option>
                    <option value="overview">Tactical Overview</option>
                    <option value="cinematic">Cinematic</option>
                    <option value="drone">Drone View</option>
                    <option value="free">Free Camera</option>
                </select>

                <label style="margin-top: 10px;">Field of View: <span id="fovValue">60</span>°</label>
                <input type="range" id="cameraFOV" min="30" max="120" value="60" style="width: 100%;">

                <label style="margin-top: 10px;">Camera Height: <span id="cameraHeightValue">15</span></label>
                <input type="range" id="cameraHeight" min="3" max="50" value="15" style="width: 100%;">

                <label style="margin-top: 10px;">Camera Distance: <span id="cameraDistanceValue">10</span></label>
                <input type="range" id="cameraDistance" min="2" max="40" value="10" style="width: 100%;">

                <label style="margin-top: 10px;">Camera Speed: <span id="cameraSpeedValue">0.1</span></label>
                <input type="range" id="cameraSpeed" min="0.02" max="0.5" step="0.01" value="0.1" style="width: 100%;">

                <label style="margin-top: 10px;">
                    <input type="checkbox" id="smoothCamera" checked> Smooth Camera Movement
                </label>

                <label>
                    <input type="checkbox" id="autoFocus"> Auto Focus on Ball
                </label>

                <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 5px;">
                    <div style="font-weight: bold; margin-bottom: 10px;">🎮 Navigation Controls</div>
                    <div style="font-size: 12px; color: #ccc; margin-bottom: 10px;">
                        Click on the 3D scene to activate navigation mode
                    </div>

                    <label>Move Speed: <span id="navSpeedValue">50</span></label>
                    <input type="range" id="navSpeed" min="10" max="200" value="50" style="width: 100%; margin: 5px 0;">

                    <label>Look Sensitivity: <span id="lookSensitivityValue">0.002</span></label>
                    <input type="range" id="lookSensitivity" min="0.001" max="0.01" step="0.001" value="0.002" style="width: 100%; margin: 5px 0;">

                    <div style="font-size: 11px; color: #aaa; margin-top: 8px;">
                        <strong>Controls:</strong> WASD (move), QE (up/down), Mouse (look), ESC (exit)
                    </div>
                </div>
            </div>
        </details>

        <details>
            <summary>🌱 Grass Settings</summary>
            <div style="margin: 10px 0;">
                <div style="margin-bottom: 15px;">
                    <label>🏔️ Displacement Strength:</label>
                    <input type="range" id="displacementStrength" min="0" max="2" step="0.1" value="0.5" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="displacementValue">0.5</span></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🌱 Grass Height:</label>
                    <input type="range" id="grassHeight" min="0.05" max="0.5" step="0.01" value="0.15" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="grassHeightValue">0.15m</span></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🌾 Grass Density:</label>
                    <input type="range" id="grassDensity" min="1.0" max="3.0" step="0.1" value="2.0" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="grassDensityValue">2.0x</span> (Minimum 2000 blades guaranteed)</div>
                </div>

                <div style="margin-top: 15px;">
                    <label>
                        <input type="checkbox" id="showWireframe" style="margin-right: 5px;"> 🔲 Show Wireframe
                    </label>
                </div>
            </div>
        </details>

        <details>
            <summary>🏟️ Stadium Environment</summary>
            <div style="margin: 10px 0;">
                <div style="margin-bottom: 15px;">
                    <label>🕐 Time of Day:</label>
                    <input type="range" id="timeOfDay" min="0" max="24" step="0.5" value="14" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="timeOfDayValue">14:00</span></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🌦️ Weather:</label>
                    <select id="weatherType" style="width: 100%; margin: 5px 0; padding: 5px;">
                        <option value="clear">☀️ Clear</option>
                        <option value="cloudy">⛅ Cloudy</option>
                        <option value="overcast">☁️ Overcast</option>
                        <option value="rain">🌧️ Rain</option>
                        <option value="storm">⛈️ Storm</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🌪️ Weather Intensity:</label>
                    <input type="range" id="weatherIntensity" min="0" max="1" step="0.1" value="0.2" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="weatherIntensityValue">0.2</span></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" id="stadiumLights" style="margin-right: 5px;"> 💡 Stadium Lights
                    </label>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🔆 Floodlight Intensity:</label>
                    <input type="range" id="floodlightIntensity" min="0" max="2" step="0.1" value="0.8" style="width: 100%; margin: 5px 0;">
                    <div style="font-size: 12px; color: #ccc;">Current: <span id="floodlightIntensityValue">0.8</span></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🎨 Floodlight Color:</label>
                    <select id="floodlightColor" style="width: 100%; margin: 5px 0; padding: 5px;">
                        <option value="0xffffff">⚪ White</option>
                        <option value="0xfff8dc">🟡 Warm White</option>
                        <option value="0x87ceeb">🔵 Cool Blue</option>
                        <option value="0xffd700">🟨 Golden</option>
                        <option value="0xff6347">🔴 Warm Orange</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label>🏟️ Stadium Style:</label>
                    <select id="stadiumStyle" style="width: 100%; margin: 5px 0; padding: 5px;">
                        <option value="modern">🏢 Modern</option>
                        <option value="classic">🏛️ Classic</option>
                        <option value="bowl">🥣 Bowl</option>
                        <option value="arena">🏟️ Arena</option>
                    </select>
                </div>

                <button id="debugStadiumInfo" style="width: 100%; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px;">
                    🔍 Debug Stadium Info
                </button>

                <button id="validateFIFACompliance" style="width: 100%; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; margin-top: 5px;">
                    ⚽ FIFA Compliance Check
                </button>
            </div>
        </details>

        <details>
            <summary>✨ Post-Processing Effects</summary>
            <div style="margin: 10px 0;">
                <label>
                    <input type="checkbox" id="enableBloom"> Bloom Effect
                </label>

                <label>
                    <input type="checkbox" id="enableSSAO"> Ambient Occlusion
                </label>

                <label>
                    <input type="checkbox" id="enableMotionBlur"> Motion Blur
                </label>

                <label style="margin-top: 10px;">Contrast: <span id="contrastValue">1.0</span></label>
                <input type="range" id="contrast" min="0.5" max="2.0" step="0.1" value="1.0" style="width: 100%;">

                <label style="margin-top: 10px;">Saturation: <span id="saturationValue">1.0</span></label>
                <input type="range" id="saturation" min="0.0" max="2.0" step="0.1" value="1.0" style="width: 100%;">

                <label style="margin-top: 10px;">Brightness: <span id="brightnessValue">0.0</span></label>
                <input type="range" id="brightness" min="-0.5" max="0.5" step="0.05" value="0.0" style="width: 100%;">
            </div>
        </details>
    </div>

    <!-- Commentary -->
    <div id="commentary">
        <h4>📢 Commentary</h4>
        <div id="commentary-content">
            <div>Welcome to Football Sim 3D!</div>
            <div>Match is about to begin...</div>
        </div>
    </div>

    <script type="module" src="main3d.js"></script>
</body>
</html>
