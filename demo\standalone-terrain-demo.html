<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🌍 Advanced Planetary Terrain Demo - Complete System</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    .parameter-slider {
      width: 100%;
      height: 6px;
      background: linear-gradient(90deg, #374151, #6b7280);
      border-radius: 3px;
      appearance: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .parameter-slider:hover {
      background: linear-gradient(90deg, #4b5563, #9ca3af);
    }
    .parameter-slider::-webkit-slider-thumb {
      appearance: none;
      width: 14px;
      height: 14px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      transition: all 0.2s ease;
    }
    .parameter-slider::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    }
    .glass-panel {
      background: rgba(17, 24, 39, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(75, 85, 99, 0.3);
    }
    .progress-overlay {
      background: rgba(0, 0, 0, 0.9);
      backdrop-filter: blur(5px);
    }
  </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white min-h-screen">
  <div class="min-h-screen p-2 sm:p-4">
    <!-- Header -->
    <div class="max-w-7xl mx-auto mb-4">
      <h1 class="text-3xl sm:text-4xl font-bold text-center mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
        🌍 Advanced Planetary Terrain Demo
      </h1>
      <p class="text-center text-gray-300 text-sm sm:text-base">Complete planetary system with atmosphere, climate, and advanced terrain generation</p>
    </div>

    <div class="max-w-7xl mx-auto grid grid-cols-1 xl:grid-cols-5 lg:grid-cols-4 gap-3">

      <!-- Controls Panel -->
      <div class="xl:col-span-1 lg:col-span-1 space-y-3 max-h-screen overflow-y-auto">

        <!-- Quick Actions -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-blue-400">⚡ Quick Actions</h3>
          <div class="grid grid-cols-2 gap-2">
            <button id="regenerate" class="px-2 py-1.5 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 rounded text-xs font-medium transition-all">
              🔄 Regenerate
            </button>
            <button id="randomize" class="px-2 py-1.5 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 rounded text-xs font-medium transition-all">
              🎲 Random
            </button>
          </div>
        </div>

        <!-- Basic Parameters -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-blue-400">🌍 Basic Parameters</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium mb-1">Planet Seed</label>
              <input type="number" id="planet-seed" value="12345" class="w-full p-1.5 bg-gray-700 border border-gray-600 rounded text-xs">
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Global Temperature</label>
              <input type="range" id="global-temp" min="200" max="400" value="288" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Cold</span>
                <span id="temp-value" class="font-mono text-yellow-400">288K</span>
                <span>Hot</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Atmospheric Pressure</label>
              <input type="range" id="atm-pressure" min="0" max="3" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="pressure-value" class="font-mono text-cyan-400">1.0x</span>
                <span>Dense</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Terrain Detail</label>
              <input type="range" id="terrain-detail" min="32" max="256" value="128" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Low</span>
                <span id="detail-value" class="font-mono text-green-400">128</span>
                <span>High</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Geological Parameters -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-orange-400">🏔️ Geological Features</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium mb-1">Tectonic Activity</label>
              <input type="range" id="tectonic-activity" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Stable</span>
                <span id="tectonic-value" class="font-mono text-orange-400">1.0x</span>
                <span>Active</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Mountain Formation</label>
              <input type="range" id="mountain-formation" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Low</span>
                <span id="mountain-value" class="font-mono text-orange-400">1.0x</span>
                <span>High</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Volcanic Activity</label>
              <input type="range" id="volcanic-activity" min="0" max="2" step="0.1" value="0.5" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="volcanic-value" class="font-mono text-red-400">0.5x</span>
                <span>Intense</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Climate Parameters -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-blue-400">🌤️ Climate & Weather</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium mb-1">Cloud Coverage</label>
              <input type="range" id="cloud-coverage" min="0" max="1" step="0.1" value="0.6" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Clear</span>
                <span id="cloud-value" class="font-mono text-blue-400">60%</span>
                <span>Overcast</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Precipitation</label>
              <input type="range" id="precipitation" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Arid</span>
                <span id="precipitation-value" class="font-mono text-blue-400">1.0x</span>
                <span>Wet</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Ocean Coverage</label>
              <input type="range" id="ocean-coverage" min="0" max="1" step="0.1" value="0.7" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Desert</span>
                <span id="ocean-value" class="font-mono text-blue-400">70%</span>
                <span>Ocean</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Atmosphere Parameters -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-purple-400">🌫️ Atmosphere</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium mb-1">Atmosphere Thickness</label>
              <input type="range" id="atmosphere-thickness" min="0.1" max="3" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Thin</span>
                <span id="thickness-value" class="font-mono text-purple-400">1.0x</span>
                <span>Dense</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Atmospheric Scattering</label>
              <input type="range" id="atmospheric-scattering" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="scattering-value" class="font-mono text-purple-400">1.0x</span>
                <span>Intense</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Environmental Features -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-green-400">🌊 Environmental</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium mb-1">Ocean Currents</label>
              <input type="range" id="ocean-currents" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Still</span>
                <span id="currents-value" class="font-mono text-green-400">1.0x</span>
                <span>Strong</span>
              </div>
            </div>

            <div>
              <label class="block text-xs font-medium mb-1">Lightning Activity</label>
              <input type="range" id="lightning-activity" min="0" max="2" step="0.1" value="0.5" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="lightning-value" class="font-mono text-yellow-400">0.5x</span>
                <span>Intense</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Visual Controls -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-pink-400">👁️ Visual Options</h3>

          <div class="space-y-2">
            <div class="grid grid-cols-2 gap-2">
              <button id="toggle-wireframe" class="px-2 py-1.5 bg-gray-600 hover:bg-gray-500 rounded text-xs transition-all">
                📐 Wireframe
              </button>
              <button id="toggle-atmosphere" class="px-2 py-1.5 bg-blue-600 hover:bg-blue-500 rounded text-xs transition-all">
                🌫️ Atmosphere
              </button>
            </div>
            <div class="grid grid-cols-2 gap-2">
              <button id="toggle-clouds" class="px-2 py-1.5 bg-cyan-600 hover:bg-cyan-500 rounded text-xs transition-all">
                ☁️ Clouds
              </button>
              <button id="toggle-currents" class="px-2 py-1.5 bg-green-600 hover:bg-green-500 rounded text-xs transition-all">
                🌊 Currents
              </button>
            </div>
          </div>
        </div>

      </div>

      <!-- 3D Canvas -->
      <div class="xl:col-span-3 lg:col-span-2">
        <div class="glass-panel p-3 rounded-lg">
          <div class="relative">
            <canvas id="terrain-canvas" class="w-full h-80 sm:h-96 lg:h-[600px] border border-gray-600 rounded bg-black"></canvas>

            <!-- Controls Overlay -->
            <div class="absolute top-2 left-2 bg-black bg-opacity-75 text-white p-2 rounded text-xs">
              <div>🖱️ Drag: Rotate</div>
              <div>🔍 Wheel: Zoom</div>
              <div>⌨️ Space: Reset View</div>
              <div class="text-yellow-400">🏔️ Zoom close for detail!</div>
              <div class="text-green-400">⭐ Icosahedron - No seams!</div>
            </div>

            <!-- Info Overlay -->
            <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white p-2 rounded text-sm">
              <div id="planet-info" class="font-bold">Advanced Planetary System</div>
              <div class="text-xs opacity-75">Complete Terrain + Atmosphere + Climate</div>
            </div>

            <!-- Progress Overlay -->
            <div id="progress-overlay" class="absolute inset-0 progress-overlay hidden items-center justify-center">
              <div class="bg-gray-800 p-6 rounded-lg border border-gray-600 max-w-md w-full mx-4">
                <div class="text-center mb-4">
                  <div id="progress-icon" class="text-4xl mb-2">🌍</div>
                  <h3 id="progress-title" class="text-lg font-semibold">Generating Planet</h3>
                  <p id="progress-description" class="text-sm text-gray-400">Preparing generation systems...</p>
                </div>

                <div class="mb-4">
                  <div class="flex justify-between text-sm mb-1">
                    <span id="progress-step">Step 1 of 12</span>
                    <span id="progress-percent">0%</span>
                  </div>
                  <div class="w-full bg-gray-700 rounded-full h-2">
                    <div id="progress-bar" class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                  </div>
                </div>

                <div class="text-center">
                  <div class="text-xs text-gray-400 mb-2">
                    <span>Vertices: </span><span id="progress-vertices" class="text-green-400">0</span>
                    <span class="ml-4">Time: </span><span id="progress-time" class="text-yellow-400">0.0s</span>
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    <button id="pause-generation-btn" class="px-3 py-1.5 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-xs transition-colors">
                      ⏸️ Pause
                    </button>
                    <button id="cancel-generation-btn" class="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded text-xs transition-colors">
                      ❌ Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Panel -->
      <div class="xl:col-span-1 lg:col-span-1 space-y-3">

        <!-- Performance Stats -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-green-400">⚡ Performance</h3>
          <div class="space-y-2 text-xs">
            <div class="flex justify-between">
              <span>FPS:</span>
              <span id="fps-value" class="font-mono text-green-400">60</span>
            </div>
            <div class="flex justify-between">
              <span>Frame Time:</span>
              <span id="frame-time-value" class="font-mono text-blue-400">16.7ms</span>
            </div>
            <div class="flex justify-between">
              <span>Status:</span>
              <span id="performance-status" class="font-mono text-green-400">Excellent</span>
            </div>
            <div class="flex justify-between">
              <span>Memory:</span>
              <span id="memory-usage" class="font-mono text-yellow-400">0MB</span>
            </div>
          </div>

          <!-- FPS Graph -->
          <div class="mt-3">
            <canvas id="fps-canvas" width="120" height="40" class="w-full h-10 bg-gray-900 rounded border border-gray-600"></canvas>
          </div>
        </div>

        <!-- Planet Stats -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-blue-400">🌍 Planet Stats</h3>
          <div class="space-y-2 text-xs">
            <div class="flex justify-between">
              <span>Vertices:</span>
              <span id="vertex-count" class="font-mono text-green-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Triangles:</span>
              <span id="triangle-count" class="font-mono text-blue-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Draw Calls:</span>
              <span id="draw-calls" class="font-mono text-purple-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Biomes:</span>
              <span id="biome-count" class="font-mono text-orange-400">8</span>
            </div>
          </div>
        </div>

        <!-- Generation Stats -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-purple-400">🔧 Generation</h3>
          <div class="space-y-2 text-xs">
            <div class="flex justify-between">
              <span>Last Gen Time:</span>
              <span id="generation-time" class="font-mono text-yellow-400">0.0s</span>
            </div>
            <div class="flex justify-between">
              <span>Weather Systems:</span>
              <span id="weather-count" class="font-mono text-cyan-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Current Lines:</span>
              <span id="current-lines" class="font-mono text-green-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Status:</span>
              <span id="generation-status" class="font-mono text-green-400">Ready</span>
            </div>
          </div>
        </div>

        <!-- Feature Toggles -->
        <div class="glass-panel p-3 rounded-lg">
          <h3 class="text-sm font-semibold mb-2 text-pink-400">🎛️ Features</h3>
          <div class="space-y-2">
            <div class="grid grid-cols-2 gap-1 text-xs">
              <button id="toggle-tectonics" class="px-2 py-1 bg-orange-600 hover:bg-orange-500 rounded transition-all">
                🏔️ Tectonics
              </button>
              <button id="toggle-climate" class="px-2 py-1 bg-blue-600 hover:bg-blue-500 rounded transition-all">
                🌡️ Climate
              </button>
              <button id="toggle-weather" class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 rounded transition-all">
                🌪️ Weather
              </button>
              <button id="toggle-environment" class="px-2 py-1 bg-green-600 hover:bg-green-500 rounded transition-all">
                🌿 Environment
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Enhanced Noise System for Advanced Planetary Generation
    class EnhancedNoise {
      static hash2D(x, y) {
        let h = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
        return h - Math.floor(h);
      }

      static hash3D(x, y, z) {
        let h = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
        return h - Math.floor(h);
      }

      static perlin3D(x, y, z, seed = 0) {
        x += seed * 0.1;
        y += seed * 0.1;
        z += seed * 0.1;

        const ix = Math.floor(x);
        const iy = Math.floor(y);
        const iz = Math.floor(z);
        const fx = x - ix;
        const fy = y - iy;
        const fz = z - iz;

        const u = fx * fx * (3 - 2 * fx);
        const v = fy * fy * (3 - 2 * fy);
        const w = fz * fz * (3 - 2 * fz);

        const c000 = this.hash3D(ix, iy, iz);
        const c100 = this.hash3D(ix + 1, iy, iz);
        const c010 = this.hash3D(ix, iy + 1, iz);
        const c110 = this.hash3D(ix + 1, iy + 1, iz);
        const c001 = this.hash3D(ix, iy, iz + 1);
        const c101 = this.hash3D(ix + 1, iy, iz + 1);
        const c011 = this.hash3D(ix, iy + 1, iz + 1);
        const c111 = this.hash3D(ix + 1, iy + 1, iz + 1);

        const x1 = c000 * (1 - u) + c100 * u;
        const x2 = c010 * (1 - u) + c110 * u;
        const x3 = c001 * (1 - u) + c101 * u;
        const x4 = c011 * (1 - u) + c111 * u;

        const y1 = x1 * (1 - v) + x2 * v;
        const y2 = x3 * (1 - v) + x4 * v;

        return y1 * (1 - w) + y2 * w;
      }

      static fractalNoise(x, y, z, octaves, persistence = 0.5, seed = 0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;

        for (let i = 0; i < octaves; i++) {
          value += this.perlin3D(x * frequency, y * frequency, z * frequency, seed + i) * amplitude;
          maxValue += amplitude;
          amplitude *= persistence;
          frequency *= 2;
        }

        return value / maxValue;
      }

      static ridgedNoise(x, y, z, octaves, seed = 0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;

        for (let i = 0; i < octaves; i++) {
          let n = Math.abs(this.perlin3D(x * frequency, y * frequency, z * frequency, seed + i));
          n = 1 - n;
          n = n * n;
          value += n * amplitude;
          amplitude *= 0.5;
          frequency *= 2;
        }

        return value;
      }

      static billowNoise(x, y, z, octaves, seed = 0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;

        for (let i = 0; i < octaves; i++) {
          let n = Math.abs(this.perlin3D(x * frequency, y * frequency, z * frequency, seed + i));
          value += n * amplitude;
          amplitude *= 0.5;
          frequency *= 2;
        }

        return value;
      }

      static domainWarp(x, y, z, strength, seed = 0) {
        const warpX = this.perlin3D(x + seed, y, z) * strength;
        const warpY = this.perlin3D(x, y + seed, z) * strength;
        const warpZ = this.perlin3D(x, y, z + seed) * strength;

        return {
          x: x + warpX,
          y: y + warpY,
          z: z + warpZ
        };
      }
    }

    // Advanced Planetary Terrain System
    class AdvancedPlanetarySystem {
      constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        // Planet parameters
        this.seed = 12345;
        this.globalTemp = 288;
        this.atmPressure = 1.0;
        this.tectonicActivity = 1.0;
        this.mountainFormation = 1.0;
        this.volcanicActivity = 0.5;
        this.cloudCoverage = 0.6;
        this.precipitation = 1.0;
        this.oceanCoverage = 0.7;
        this.atmosphereThickness = 1.0;
        this.atmosphericScattering = 1.0;
        this.oceanCurrents = 1.0;
        this.lightningActivity = 0.5;

        // Terrain parameters
        this.terrainDetail = 128;

        // Visual features
        this.features = {
          atmosphere: true,
          clouds: true,
          currents: true,
          weather: true,
          tectonics: true,
          climate: true,
          environment: true
        };

        // Meshes
        this.planetMesh = null;
        this.atmosphereMesh = null;
        this.stratosphereMesh = null;
        this.mesosphereMesh = null;
        this.cloudMeshes = [];
        this.oceanCurrentLines = [];

        // Performance tracking
        this.fpsHistory = [];
        this.frameTimeHistory = [];
        this.lastFrameTime = performance.now();
        this.fpsCanvas = null;
        this.fpsCtx = null;

        // Generation state
        this.isGenerating = false;
        this.generationCancelled = false;
        this.generationPaused = false;
        this.generationStartTime = 0;
        this.currentStep = 0;
        this.totalSteps = 12;

        // Weather systems
        this.weatherSystems = [];

        this.initialize();
      }

      initialize() {
        this.initializeScene();
        this.initializeControls();
        this.initializePerformanceTracking();
        this.generatePlanet();
        this.animate();
      }

      initializeScene() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);

        // Camera setup
        const canvas = document.getElementById('terrain-canvas');
        const aspect = canvas.clientWidth / canvas.clientHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
        this.camera.position.set(0, 0, 3);

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({
          canvas: canvas,
          antialias: true,
          alpha: true
        });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Mouse controls
        this.setupMouseControls();

        // Window resize handler
        window.addEventListener('resize', () => this.onWindowResize());
      }

      setupMouseControls() {
        const canvas = document.getElementById('terrain-canvas');
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };
        let rotationX = 0;
        let rotationY = 0;
        let zoom = 3;

        canvas.addEventListener('mousedown', (e) => {
          isDragging = true;
          previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        canvas.addEventListener('mousemove', (e) => {
          if (!isDragging) return;

          const deltaX = e.clientX - previousMousePosition.x;
          const deltaY = e.clientY - previousMousePosition.y;

          rotationY += deltaX * 0.01;
          rotationX += deltaY * 0.01;

          rotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, rotationX));

          this.camera.position.x = Math.sin(rotationY) * Math.cos(rotationX) * zoom;
          this.camera.position.y = Math.sin(rotationX) * zoom;
          this.camera.position.z = Math.cos(rotationY) * Math.cos(rotationX) * zoom;
          this.camera.lookAt(0, 0, 0);

          previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        canvas.addEventListener('mouseup', () => {
          isDragging = false;
        });

        canvas.addEventListener('wheel', (e) => {
          e.preventDefault();
          zoom += e.deltaY * 0.001;
          zoom = Math.max(1.5, Math.min(20, zoom));

          this.camera.position.x = Math.sin(rotationY) * Math.cos(rotationX) * zoom;
          this.camera.position.y = Math.sin(rotationX) * zoom;
          this.camera.position.z = Math.cos(rotationY) * Math.cos(rotationX) * zoom;
          this.camera.lookAt(0, 0, 0);
        });

        // Reset view on spacebar
        document.addEventListener('keydown', (e) => {
          if (e.code === 'Space') {
            e.preventDefault();
            rotationX = 0;
            rotationY = 0;
            zoom = 3;
            this.camera.position.set(0, 0, 3);
            this.camera.lookAt(0, 0, 0);
          }
        });
      }

      initializePerformanceTracking() {
        this.fpsCanvas = document.getElementById('fps-canvas');
        if (this.fpsCanvas) {
          this.fpsCtx = this.fpsCanvas.getContext('2d');
        }
      }

      async generatePlanet() {
        if (this.isGenerating) return;

        this.isGenerating = true;
        this.generationCancelled = false;
        this.generationPaused = false;
        this.generationStartTime = performance.now();

        try {
          this.showProgress();
          this.clearPlanetMeshes();

          // Step 1: Generate base geometry
          this.updateProgress('Creating Base Geometry', 'Generating icosahedron mesh...', 'geometry');
          const geometry = this.createIcosahedronGeometry(1.0, this.terrainDetail);
          await this.delay(50);

          // Step 2: Apply tectonic processes
          if (this.features.tectonics) {
            this.updateProgress('Applying Tectonic Processes', 'Creating mountains and continental structure...', 'tectonics');
            await this.applyTectonicProcesses(geometry);
            await this.delay(50);
          }

          // Step 3: Apply climate effects
          if (this.features.climate) {
            this.updateProgress('Calculating Climate Effects', 'Determining biomes and surface colors...', 'climate');
            await this.applyClimateEffects(geometry);
            await this.delay(50);
          }

          // Step 4: Create planet mesh
          this.updateProgress('Creating Planet Mesh', 'Finalizing planet surface...', 'finalize');
          this.createPlanetMesh(geometry);
          await this.delay(50);

          // Step 5: Create atmosphere
          if (this.features.atmosphere && this.atmPressure > 0.1) {
            this.updateProgress('Creating Atmosphere', 'Generating atmospheric layers...', 'atmosphere');
            this.createAtmosphere();
            await this.delay(50);
          }

          // Step 6: Create clouds
          if (this.features.clouds && this.cloudCoverage > 0.1) {
            this.updateProgress('Creating Clouds', 'Generating cloud layers...', 'clouds');
            this.createClouds();
            await this.delay(50);
          }

          // Step 7: Generate weather systems
          if (this.features.weather) {
            this.updateProgress('Creating Weather Systems', 'Generating atmospheric dynamics...', 'weather');
            this.generateWeatherSystems();
            await this.delay(50);
          }

          // Step 8: Create ocean currents
          if (this.features.currents && this.oceanCurrents > 0.1) {
            this.updateProgress('Creating Ocean Currents', 'Generating oceanic circulation...', 'environment');
            this.createOceanCurrentVisualization();
            await this.delay(50);
          }

          // Complete
          this.updateProgress('Generation Complete', 'Planet successfully generated!', 'complete');
          await this.delay(500);

          this.hideProgress();
          this.updateStats();

        } catch (error) {
          if (error.message === 'Generation cancelled') {
            console.log('Planet generation cancelled by user');
          } else {
            console.error('Error generating planet:', error);
          }
          this.hideProgress();
        }
      }

      createIcosahedronGeometry(radius, subdivisions) {
        // Create base icosahedron
        const t = (1.0 + Math.sqrt(5.0)) / 2.0;

        const vertices = [
          [-1,  t,  0], [ 1,  t,  0], [-1, -t,  0], [ 1, -t,  0],
          [ 0, -1,  t], [ 0,  1,  t], [ 0, -1, -t], [ 0,  1, -t],
          [ t,  0, -1], [ t,  0,  1], [-t,  0, -1], [-t,  0,  1]
        ];

        // Normalize vertices
        for (let i = 0; i < vertices.length; i++) {
          const v = vertices[i];
          const length = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
          vertices[i] = [v[0] / length, v[1] / length, v[2] / length];
        }

        const faces = [
          [0, 11, 5], [0, 5, 1], [0, 1, 7], [0, 7, 10], [0, 10, 11],
          [1, 5, 9], [5, 11, 4], [11, 10, 2], [10, 7, 6], [7, 1, 8],
          [3, 9, 4], [3, 4, 2], [3, 2, 6], [3, 6, 8], [3, 8, 9],
          [4, 9, 5], [2, 4, 11], [6, 2, 10], [8, 6, 7], [9, 8, 1]
        ];

        // Subdivide faces
        for (let i = 0; i < Math.log2(subdivisions / 20); i++) {
          const newFaces = [];
          const midpointCache = {};

          const getMidpoint = (i1, i2) => {
            const key = `${Math.min(i1, i2)}-${Math.max(i1, i2)}`;
            if (midpointCache[key] !== undefined) {
              return midpointCache[key];
            }

            const v1 = vertices[i1];
            const v2 = vertices[i2];
            const mid = [
              (v1[0] + v2[0]) / 2,
              (v1[1] + v2[1]) / 2,
              (v1[2] + v2[2]) / 2
            ];

            // Normalize to sphere
            const length = Math.sqrt(mid[0] * mid[0] + mid[1] * mid[1] + mid[2] * mid[2]);
            mid[0] /= length;
            mid[1] /= length;
            mid[2] /= length;

            vertices.push(mid);
            midpointCache[key] = vertices.length - 1;
            return vertices.length - 1;
          };

          for (const face of faces) {
            const a = getMidpoint(face[0], face[1]);
            const b = getMidpoint(face[1], face[2]);
            const c = getMidpoint(face[2], face[0]);

            newFaces.push([face[0], a, c]);
            newFaces.push([face[1], b, a]);
            newFaces.push([face[2], c, b]);
            newFaces.push([a, b, c]);
          }

          faces.length = 0;
          faces.push(...newFaces);
        }

        // Convert to Three.js geometry
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(faces.length * 9);
        const normals = new Float32Array(faces.length * 9);

        for (let i = 0; i < faces.length; i++) {
          const face = faces[i];
          for (let j = 0; j < 3; j++) {
            const vertex = vertices[face[j]];
            const idx = i * 9 + j * 3;

            positions[idx] = vertex[0] * radius;
            positions[idx + 1] = vertex[1] * radius;
            positions[idx + 2] = vertex[2] * radius;

            normals[idx] = vertex[0];
            normals[idx + 1] = vertex[1];
            normals[idx + 2] = vertex[2];
          }
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('normal', new THREE.BufferAttribute(normals, 3));

        return geometry;
      }

      async applyTectonicProcesses(geometry) {
        const positions = geometry.attributes.position;
        const vertex = new THREE.Vector3();

        const totalVertices = positions.count;
        let processedVertices = 0;
        const chunkSize = 1000;

        for (let startIndex = 0; startIndex < totalVertices; startIndex += chunkSize) {
          if (this.generationCancelled) throw new Error('Generation cancelled');

          while (this.generationPaused) {
            await this.delay(100);
            if (this.generationCancelled) throw new Error('Generation cancelled');
          }

          const endIndex = Math.min(startIndex + chunkSize, totalVertices);

          for (let i = startIndex; i < endIndex; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();

            // Apply domain warping for realistic terrain
            const warpedPos = EnhancedNoise.domainWarp(normal.x, normal.y, normal.z, 0.3, this.seed);

            // Continental/oceanic crust distribution
            const continentalCrust = this.calculateContinentalCrust(warpedPos);

            // Mountain formation
            const mountainElevation = this.calculateMountainElevation(warpedPos, continentalCrust);

            // Volcanic features
            const volcanicElevation = this.calculateVolcanicElevation(warpedPos);

            // Combine elevations
            let finalElevation = 0;

            if (continentalCrust < 0.3) {
              // Oceanic regions
              finalElevation = -0.05 - Math.random() * 0.1;
              finalElevation += volcanicElevation * 0.3;
            } else {
              // Continental regions
              finalElevation = continentalCrust * 0.02;
              finalElevation += mountainElevation * this.mountainFormation;
              finalElevation += volcanicElevation * this.volcanicActivity;
            }

            // Apply tectonic activity scaling
            finalElevation *= this.tectonicActivity;

            // Apply to vertex
            const newLength = 1.0 + finalElevation;
            vertex.setLength(newLength);

            positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
          }

          processedVertices = endIndex;

          // Update progress
          if (document.getElementById('progress-vertices')) {
            document.getElementById('progress-vertices').textContent = processedVertices.toLocaleString();
          }

          await this.delay(16);
        }

        positions.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      calculateContinentalCrust(pos) {
        // Large-scale continental distribution
        const continentalNoise = EnhancedNoise.fractalNoise(
          pos.x * 2, pos.y * 2, pos.z * 2, 4, 0.6, this.seed
        );

        // Add some randomness for realistic distribution
        const randomFactor = EnhancedNoise.perlin3D(pos.x * 5, pos.y * 5, pos.z * 5, this.seed + 100);

        return Math.max(0, continentalNoise + randomFactor * 0.3);
      }

      calculateMountainElevation(pos, continentalCrust) {
        if (continentalCrust < 0.2) return 0; // No mountains in deep ocean

        // Ridged noise for mountain ranges
        const mountainRidges = EnhancedNoise.ridgedNoise(
          pos.x * 8, pos.y * 8, pos.z * 8, 6, this.seed + 200
        );

        // Fractal noise for general elevation
        const generalElevation = EnhancedNoise.fractalNoise(
          pos.x * 4, pos.y * 4, pos.z * 4, 8, 0.5, this.seed + 300
        );

        return (mountainRidges * 0.15 + generalElevation * 0.08) * continentalCrust;
      }

      calculateVolcanicElevation(pos) {
        // Volcanic hotspots
        const volcanicNoise = EnhancedNoise.billowNoise(
          pos.x * 12, pos.y * 12, pos.z * 12, 4, this.seed + 400
        );

        // Create isolated volcanic peaks
        const threshold = 0.7;
        if (volcanicNoise > threshold) {
          return (volcanicNoise - threshold) * 0.3;
        }

        return 0;
      }

      async applyClimateEffects(geometry) {
        const positions = geometry.attributes.position;
        const colors = new Float32Array(positions.count * 3);
        const vertex = new THREE.Vector3();

        const totalVertices = positions.count;
        let processedVertices = 0;
        const chunkSize = 1000;

        for (let startIndex = 0; startIndex < totalVertices; startIndex += chunkSize) {
          if (this.generationCancelled) throw new Error('Generation cancelled');

          while (this.generationPaused) {
            await this.delay(100);
            if (this.generationCancelled) throw new Error('Generation cancelled');
          }

          const endIndex = Math.min(startIndex + chunkSize, totalVertices);

          for (let i = startIndex; i < endIndex; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();
            const elevation = vertex.length() - 1.0;

            // Calculate climate
            const latitude = Math.asin(normal.y);
            const temperature = this.globalTemp * (1 - Math.abs(latitude) * 0.3) - elevation * 50;
            const precipitation = this.precipitation * (1 + Math.cos(latitude * 2) * 0.5);

            // Determine biome and color
            const biomeColor = this.getBiomeColor(elevation, temperature, precipitation, normal);

            colors[i * 3] = biomeColor.r;
            colors[i * 3 + 1] = biomeColor.g;
            colors[i * 3 + 2] = biomeColor.b;
          }

          processedVertices = endIndex;

          if (document.getElementById('progress-vertices')) {
            document.getElementById('progress-vertices').textContent = processedVertices.toLocaleString();
          }

          await this.delay(16);
        }

        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      }

      getBiomeColor(elevation, temperature, precipitation, normal) {
        const latitude = Math.abs(Math.asin(normal.y));

        // Ocean biomes
        if (elevation < -0.01) {
          const depth = Math.abs(elevation);
          if (depth > 0.1) {
            return { r: 0.02, g: 0.08, b: 0.25 };
          } else {
            return { r: 0.1, g: 0.3, b: 0.6 };
          }
        }

        // Ice/Snow
        if (temperature < 250) {
          return { r: 0.9, g: 0.95, b: 1.0 };
        }

        // Land biomes based on temperature and precipitation
        if (temperature < 270) {
          // Tundra
          return { r: 0.6, g: 0.7, b: 0.5 };
        } else if (temperature < 290) {
          if (precipitation > 1.5) {
            // Boreal forest
            return { r: 0.2, g: 0.5, b: 0.2 };
          } else {
            // Grassland
            return { r: 0.4, g: 0.6, b: 0.3 };
          }
        } else if (temperature < 310) {
          if (precipitation > 1.8) {
            // Temperate forest
            return { r: 0.3, g: 0.6, b: 0.2 };
          } else if (precipitation > 0.8) {
            // Temperate grassland
            return { r: 0.5, g: 0.7, b: 0.3 };
          } else {
            // Steppe
            return { r: 0.7, g: 0.6, b: 0.4 };
          }
        } else {
          if (precipitation > 2.0) {
            // Tropical rainforest
            return { r: 0.2, g: 0.7, b: 0.2 };
          } else if (precipitation > 1.0) {
            // Tropical savanna
            return { r: 0.6, g: 0.7, b: 0.3 };
          } else {
            // Desert
            return { r: 0.8, g: 0.7, b: 0.4 };
          }
        }
      }

      createPlanetMesh(geometry) {
        const material = new THREE.MeshLambertMaterial({
          vertexColors: true,
          side: THREE.DoubleSide
        });

        if (this.planetMesh) {
          this.scene.remove(this.planetMesh);
          this.planetMesh.geometry.dispose();
          this.planetMesh.material.dispose();
        }

        this.planetMesh = new THREE.Mesh(geometry, material);
        this.planetMesh.castShadow = true;
        this.planetMesh.receiveShadow = true;
        this.scene.add(this.planetMesh);
      }

      createAtmosphere() {
        if (this.atmPressure < 0.1) return;

        // Main atmosphere layer
        const atmosphereGeometry = new THREE.SphereGeometry(1.05 + this.atmosphereThickness * 0.03, 64, 64);
        const atmosphereMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(0.4, 0.7, 1.0),
          transparent: true,
          opacity: this.atmPressure * 0.2 * this.atmosphereThickness,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        if (this.atmosphereMesh) {
          this.scene.remove(this.atmosphereMesh);
          this.atmosphereMesh.geometry.dispose();
          this.atmosphereMesh.material.dispose();
        }

        this.atmosphereMesh = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        this.scene.add(this.atmosphereMesh);

        // Stratosphere layer
        const stratosphereGeometry = new THREE.SphereGeometry(1.08 + this.atmosphereThickness * 0.04, 48, 48);
        const stratosphereMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.65, 0.6, 0.4),
          transparent: true,
          opacity: this.atmPressure * 0.03 * this.atmosphereThickness,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        if (this.stratosphereMesh) {
          this.scene.remove(this.stratosphereMesh);
          this.stratosphereMesh.geometry.dispose();
          this.stratosphereMesh.material.dispose();
        }

        this.stratosphereMesh = new THREE.Mesh(stratosphereGeometry, stratosphereMaterial);
        this.scene.add(this.stratosphereMesh);
      }

      createClouds() {
        if (this.cloudCoverage < 0.1) return;

        // Clear existing clouds
        this.cloudMeshes.forEach(mesh => {
          this.scene.remove(mesh);
          mesh.geometry.dispose();
          mesh.material.dispose();
        });
        this.cloudMeshes = [];

        // Low clouds
        const lowCloudGeometry = new THREE.SphereGeometry(1.02, 32, 32);
        const lowCloudMaterial = new THREE.MeshBasicMaterial({
          color: 0xffffff,
          transparent: true,
          opacity: this.cloudCoverage * 0.3,
          side: THREE.DoubleSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const lowCloudMesh = new THREE.Mesh(lowCloudGeometry, lowCloudMaterial);
        this.cloudMeshes.push(lowCloudMesh);
        this.scene.add(lowCloudMesh);

        // High clouds
        const highCloudGeometry = new THREE.SphereGeometry(1.04, 24, 24);
        const highCloudMaterial = new THREE.MeshBasicMaterial({
          color: 0xffffff,
          transparent: true,
          opacity: this.cloudCoverage * 0.2,
          side: THREE.DoubleSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const highCloudMesh = new THREE.Mesh(highCloudGeometry, highCloudMaterial);
        this.cloudMeshes.push(highCloudMesh);
        this.scene.add(highCloudMesh);
      }

      generateWeatherSystems() {
        this.weatherSystems = [];

        const systemCount = Math.min(8, Math.floor(this.precipitation * 4 + 2));

        for (let i = 0; i < systemCount; i++) {
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          const center = {
            x: Math.sin(phi) * Math.cos(theta),
            y: Math.cos(phi),
            z: Math.sin(phi) * Math.sin(theta)
          };

          const systemType = Math.random() < 0.6 ? 'cyclone' : 'anticyclone';

          this.weatherSystems.push({
            center: center,
            type: systemType,
            intensity: 0.3 + Math.random() * 0.7,
            radius: 0.08 + Math.random() * 0.15,
            age: Math.random() * 14
          });
        }
      }

      createOceanCurrentVisualization() {
        if (this.oceanCurrents < 0.1) return;

        // Clear existing currents
        this.clearOceanCurrents();

        const currentCount = Math.floor(this.oceanCurrents * 6 + 2);

        for (let i = 0; i < currentCount; i++) {
          const currentPath = this.generateCurrentPath();

          const geometry = new THREE.BufferGeometry();
          const positions = new Float32Array(currentPath.length * 3);
          const colors = new Float32Array(currentPath.length * 3);

          for (let j = 0; j < currentPath.length; j++) {
            positions[j * 3] = currentPath[j].x;
            positions[j * 3 + 1] = currentPath[j].y;
            positions[j * 3 + 2] = currentPath[j].z;

            const strength = currentPath[j].strength || 0.5;
            const temp = currentPath[j].temperature || 0.5;

            colors[j * 3] = temp * 0.8 + 0.2;
            colors[j * 3 + 1] = 0.4 + strength * 0.4;
            colors[j * 3 + 2] = (1 - temp) * 0.8 + 0.2;
          }

          geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
          geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

          const material = new THREE.LineBasicMaterial({
            vertexColors: true,
            transparent: true,
            opacity: this.oceanCurrents * 0.6,
            linewidth: 2
          });

          const currentLine = new THREE.Line(geometry, material);
          this.oceanCurrentLines.push(currentLine);
          this.scene.add(currentLine);
        }
      }

      generateCurrentPath() {
        const pathLength = 15 + Math.floor(Math.random() * 20);
        const path = [];

        let theta = Math.random() * Math.PI * 2;
        let phi = Math.PI * 0.3 + Math.random() * Math.PI * 0.4;

        for (let i = 0; i < pathLength; i++) {
          const currentPos = {
            x: 1.005 * Math.sin(phi) * Math.cos(theta),
            y: 1.005 * Math.cos(phi),
            z: 1.005 * Math.sin(phi) * Math.sin(theta)
          };

          const latitude = Math.abs(currentPos.y);
          const strength = 0.3 + Math.random() * 0.7;
          const temperature = Math.max(0, 1 - latitude * 1.5);

          path.push({
            x: currentPos.x,
            y: currentPos.y,
            z: currentPos.z,
            strength: strength,
            temperature: temperature
          });

          // Update direction
          const coriolisEffect = currentPos.y * 0.1;
          theta += coriolisEffect + (Math.random() - 0.5) * 0.2;
          phi += (Math.random() - 0.5) * 0.1;
          phi = Math.max(0.2, Math.min(Math.PI - 0.2, phi));
        }

        return path;
      }

      clearPlanetMeshes() {
        if (this.planetMesh) {
          this.scene.remove(this.planetMesh);
          this.planetMesh.geometry.dispose();
          this.planetMesh.material.dispose();
          this.planetMesh = null;
        }

        if (this.atmosphereMesh) {
          this.scene.remove(this.atmosphereMesh);
          this.atmosphereMesh.geometry.dispose();
          this.atmosphereMesh.material.dispose();
          this.atmosphereMesh = null;
        }

        if (this.stratosphereMesh) {
          this.scene.remove(this.stratosphereMesh);
          this.stratosphereMesh.geometry.dispose();
          this.stratosphereMesh.material.dispose();
          this.stratosphereMesh = null;
        }

        this.cloudMeshes.forEach(mesh => {
          this.scene.remove(mesh);
          mesh.geometry.dispose();
          mesh.material.dispose();
        });
        this.cloudMeshes = [];

        this.clearOceanCurrents();
      }

      clearOceanCurrents() {
        this.oceanCurrentLines.forEach(line => {
          this.scene.remove(line);
          line.geometry.dispose();
          line.material.dispose();
        });
        this.oceanCurrentLines = [];
      }

      initializeControls() {
        // Basic parameter controls
        document.getElementById('planet-seed').addEventListener('input', (e) => {
          this.seed = parseInt(e.target.value);
        });

        document.getElementById('global-temp').addEventListener('input', (e) => {
          this.globalTemp = parseFloat(e.target.value);
          document.getElementById('temp-value').textContent = Math.round(this.globalTemp) + 'K';
        });

        document.getElementById('atm-pressure').addEventListener('input', (e) => {
          this.atmPressure = parseFloat(e.target.value);
          document.getElementById('pressure-value').textContent = e.target.value + 'x';
        });

        document.getElementById('terrain-detail').addEventListener('input', (e) => {
          this.terrainDetail = parseInt(e.target.value);
          document.getElementById('detail-value').textContent = e.target.value;
        });

        // Geological controls
        document.getElementById('tectonic-activity').addEventListener('input', (e) => {
          this.tectonicActivity = parseFloat(e.target.value);
          document.getElementById('tectonic-value').textContent = e.target.value + 'x';
        });

        document.getElementById('mountain-formation').addEventListener('input', (e) => {
          this.mountainFormation = parseFloat(e.target.value);
          document.getElementById('mountain-value').textContent = e.target.value + 'x';
        });

        document.getElementById('volcanic-activity').addEventListener('input', (e) => {
          this.volcanicActivity = parseFloat(e.target.value);
          document.getElementById('volcanic-value').textContent = e.target.value + 'x';
        });

        // Climate controls
        document.getElementById('cloud-coverage').addEventListener('input', (e) => {
          this.cloudCoverage = parseFloat(e.target.value);
          document.getElementById('cloud-value').textContent = Math.round(this.cloudCoverage * 100) + '%';
        });

        document.getElementById('precipitation').addEventListener('input', (e) => {
          this.precipitation = parseFloat(e.target.value);
          document.getElementById('precipitation-value').textContent = e.target.value + 'x';
        });

        document.getElementById('ocean-coverage').addEventListener('input', (e) => {
          this.oceanCoverage = parseFloat(e.target.value);
          document.getElementById('ocean-value').textContent = Math.round(this.oceanCoverage * 100) + '%';
        });

        // Atmosphere controls
        document.getElementById('atmosphere-thickness').addEventListener('input', (e) => {
          this.atmosphereThickness = parseFloat(e.target.value);
          document.getElementById('thickness-value').textContent = e.target.value + 'x';
        });

        document.getElementById('atmospheric-scattering').addEventListener('input', (e) => {
          this.atmosphericScattering = parseFloat(e.target.value);
          document.getElementById('scattering-value').textContent = e.target.value + 'x';
        });

        // Environmental controls
        document.getElementById('ocean-currents').addEventListener('input', (e) => {
          this.oceanCurrents = parseFloat(e.target.value);
          document.getElementById('currents-value').textContent = e.target.value + 'x';
        });

        document.getElementById('lightning-activity').addEventListener('input', (e) => {
          this.lightningActivity = parseFloat(e.target.value);
          document.getElementById('lightning-value').textContent = e.target.value + 'x';
        });

        // Action buttons
        document.getElementById('regenerate').addEventListener('click', () => {
          this.generatePlanet();
        });

        document.getElementById('randomize').addEventListener('click', () => {
          this.randomizePlanet();
        });

        // Visual toggles
        document.getElementById('toggle-wireframe').addEventListener('click', () => {
          if (this.planetMesh) {
            this.planetMesh.material.wireframe = !this.planetMesh.material.wireframe;
          }
        });

        document.getElementById('toggle-atmosphere').addEventListener('click', () => {
          this.features.atmosphere = !this.features.atmosphere;
          if (this.atmosphereMesh) this.atmosphereMesh.visible = this.features.atmosphere;
          if (this.stratosphereMesh) this.stratosphereMesh.visible = this.features.atmosphere;
        });

        document.getElementById('toggle-clouds').addEventListener('click', () => {
          this.features.clouds = !this.features.clouds;
          this.cloudMeshes.forEach(mesh => {
            mesh.visible = this.features.clouds;
          });
        });

        document.getElementById('toggle-currents').addEventListener('click', () => {
          this.features.currents = !this.features.currents;
          this.oceanCurrentLines.forEach(line => {
            line.visible = this.features.currents;
          });
        });

        // Feature toggles
        document.getElementById('toggle-tectonics').addEventListener('click', () => {
          this.features.tectonics = !this.features.tectonics;
        });

        document.getElementById('toggle-climate').addEventListener('click', () => {
          this.features.climate = !this.features.climate;
        });

        document.getElementById('toggle-weather').addEventListener('click', () => {
          this.features.weather = !this.features.weather;
        });

        document.getElementById('toggle-environment').addEventListener('click', () => {
          this.features.environment = !this.features.environment;
        });

        // Generation controls
        if (document.getElementById('pause-generation-btn')) {
          document.getElementById('pause-generation-btn').addEventListener('click', () => {
            this.togglePauseGeneration();
          });
        }

        if (document.getElementById('cancel-generation-btn')) {
          document.getElementById('cancel-generation-btn').addEventListener('click', () => {
            this.cancelGeneration();
          });
        }
      }

      randomizePlanet() {
        if (this.isGenerating) return;

        this.seed = Math.floor(Math.random() * 1000000);
        this.globalTemp = 200 + Math.random() * 200;
        this.atmPressure = Math.random() * 3;
        this.tectonicActivity = Math.random() * 2;
        this.mountainFormation = Math.random() * 2;
        this.volcanicActivity = Math.random() * 2;
        this.cloudCoverage = Math.random();
        this.precipitation = Math.random() * 2;
        this.oceanCoverage = Math.random();
        this.atmosphereThickness = 0.5 + Math.random() * 1.5;

        // Update UI
        document.getElementById('planet-seed').value = this.seed.toString();
        document.getElementById('global-temp').value = this.globalTemp.toString();
        document.getElementById('atm-pressure').value = this.atmPressure.toString();
        document.getElementById('tectonic-activity').value = this.tectonicActivity.toString();
        document.getElementById('mountain-formation').value = this.mountainFormation.toString();
        document.getElementById('volcanic-activity').value = this.volcanicActivity.toString();
        document.getElementById('cloud-coverage').value = this.cloudCoverage.toString();
        document.getElementById('precipitation').value = this.precipitation.toString();
        document.getElementById('ocean-coverage').value = this.oceanCoverage.toString();
        document.getElementById('atmosphere-thickness').value = this.atmosphereThickness.toString();

        // Update displays
        document.getElementById('temp-value').textContent = Math.round(this.globalTemp) + 'K';
        document.getElementById('pressure-value').textContent = this.atmPressure.toFixed(1) + 'x';
        document.getElementById('tectonic-value').textContent = this.tectonicActivity.toFixed(1) + 'x';
        document.getElementById('mountain-value').textContent = this.mountainFormation.toFixed(1) + 'x';
        document.getElementById('volcanic-value').textContent = this.volcanicActivity.toFixed(1) + 'x';
        document.getElementById('cloud-value').textContent = Math.round(this.cloudCoverage * 100) + '%';
        document.getElementById('precipitation-value').textContent = this.precipitation.toFixed(1) + 'x';
        document.getElementById('ocean-value').textContent = Math.round(this.oceanCoverage * 100) + '%';
        document.getElementById('thickness-value').textContent = this.atmosphereThickness.toFixed(1) + 'x';

        this.generatePlanet();
      }

      togglePauseGeneration() {
        if (!this.isGenerating) return;

        this.generationPaused = !this.generationPaused;

        const pauseBtn = document.getElementById('pause-generation-btn');
        if (pauseBtn) {
          pauseBtn.textContent = this.generationPaused ? '▶️ Resume' : '⏸️ Pause';
        }
      }

      cancelGeneration() {
        if (!this.isGenerating) return;

        this.generationCancelled = true;
        this.generationPaused = false;

        setTimeout(() => {
          this.hideProgress();
        }, 1000);
      }

      updateProgress(title, description, icon) {
        const iconMap = {
          'geometry': '🏔️',
          'tectonics': '🌋',
          'climate': '🌡️',
          'atmosphere': '🌫️',
          'clouds': '☁️',
          'weather': '🌪️',
          'environment': '🌊',
          'finalize': '🎯',
          'complete': '✅'
        };

        if (document.getElementById('progress-title')) {
          document.getElementById('progress-title').textContent = title;
        }
        if (document.getElementById('progress-description')) {
          document.getElementById('progress-description').textContent = description;
        }
        if (document.getElementById('progress-icon')) {
          document.getElementById('progress-icon').textContent = iconMap[icon] || '🌍';
        }

        const progress = (this.currentStep / this.totalSteps) * 100;
        if (document.getElementById('progress-bar')) {
          document.getElementById('progress-bar').style.width = progress + '%';
        }
        if (document.getElementById('progress-step')) {
          document.getElementById('progress-step').textContent = `Step ${this.currentStep} of ${this.totalSteps}`;
        }
        if (document.getElementById('progress-percent')) {
          document.getElementById('progress-percent').textContent = Math.round(progress) + '%';
        }

        const elapsed = (performance.now() - this.generationStartTime) / 1000;
        if (document.getElementById('progress-time')) {
          document.getElementById('progress-time').textContent = elapsed.toFixed(1) + 's';
        }

        this.currentStep++;
      }

      showProgress() {
        const progressOverlay = document.getElementById('progress-overlay');
        if (progressOverlay) {
          progressOverlay.style.display = 'flex';
        }

        this.generationStartTime = performance.now();
        this.currentStep = 1;
      }

      hideProgress() {
        const progressOverlay = document.getElementById('progress-overlay');
        if (progressOverlay) {
          progressOverlay.style.display = 'none';
        }

        this.isGenerating = false;
        this.generationCancelled = false;
        this.generationPaused = false;
      }

      delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      }

      updateStats() {
        if (this.planetMesh && this.planetMesh.geometry) {
          const vertices = this.planetMesh.geometry.attributes.position.count;
          const triangles = vertices / 3;

          document.getElementById('vertex-count').textContent = vertices.toLocaleString();
          document.getElementById('triangle-count').textContent = Math.floor(triangles).toLocaleString();
        }

        let drawCalls = 0;
        if (this.planetMesh) drawCalls++;
        if (this.atmosphereMesh && this.atmosphereMesh.visible) drawCalls++;
        if (this.stratosphereMesh && this.stratosphereMesh.visible) drawCalls++;
        drawCalls += this.cloudMeshes.filter(mesh => mesh.visible).length;
        drawCalls += this.oceanCurrentLines.filter(line => line.visible).length;

        document.getElementById('draw-calls').textContent = drawCalls;
        document.getElementById('weather-count').textContent = this.weatherSystems.length;
        document.getElementById('current-lines').textContent = this.oceanCurrentLines.length;

        const generationTime = this.isGenerating ?
          ((performance.now() - this.generationStartTime) / 1000).toFixed(1) + 's' :
          'Ready';
        document.getElementById('generation-time').textContent = generationTime;
        document.getElementById('generation-status').textContent = this.isGenerating ? 'Generating...' : 'Ready';
      }

      updatePerformanceStats(fps, frameTime) {
        this.fpsHistory.push(fps);
        if (this.fpsHistory.length > 60) {
          this.fpsHistory.shift();
        }

        this.frameTimeHistory.push(frameTime);
        if (this.frameTimeHistory.length > 60) {
          this.frameTimeHistory.shift();
        }

        const avgFps = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
        const avgFrameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;

        document.getElementById('fps-value').textContent = Math.round(avgFps);
        document.getElementById('frame-time-value').textContent = avgFrameTime.toFixed(1) + 'ms';

        let status = 'Excellent';
        let statusColor = '#10b981';

        if (avgFps < 20) {
          status = 'Critical';
          statusColor = '#dc2626';
        } else if (avgFps < 30) {
          status = 'Poor';
          statusColor = '#ef4444';
        } else if (avgFps < 45) {
          status = 'Fair';
          statusColor = '#f59e0b';
        } else if (avgFps < 55) {
          status = 'Good';
          statusColor = '#3b82f6';
        }

        const statusElement = document.getElementById('performance-status');
        if (statusElement) {
          statusElement.textContent = status;
          statusElement.style.color = statusColor;
        }

        // Estimate memory usage
        let memoryMB = 0;
        if (this.planetMesh && this.planetMesh.geometry) {
          const vertices = this.planetMesh.geometry.attributes.position.count;
          memoryMB += (vertices * 12) / (1024 * 1024);
        }
        memoryMB += this.cloudMeshes.length * 2;
        memoryMB += this.oceanCurrentLines.length * 0.1;

        document.getElementById('memory-usage').textContent = memoryMB.toFixed(1) + 'MB';

        this.drawFpsGraph();
      }

      drawFpsGraph() {
        if (!this.fpsCtx) return;

        const ctx = this.fpsCtx;
        const width = this.fpsCanvas.width;
        const height = this.fpsCanvas.height;

        ctx.fillStyle = '#111827';
        ctx.fillRect(0, 0, width, height);

        ctx.strokeStyle = '#10b981';
        ctx.lineWidth = 1;
        ctx.beginPath();

        for (let i = 0; i < this.fpsHistory.length; i++) {
          const x = (i / (this.fpsHistory.length - 1)) * width;
          const y = height - (this.fpsHistory[i] / 120) * height;

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }

        ctx.stroke();
      }

      onWindowResize() {
        const canvas = document.getElementById('terrain-canvas');
        const aspect = canvas.clientWidth / canvas.clientHeight;

        this.camera.aspect = aspect;
        this.camera.updateProjectionMatrix();

        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
      }

      animate() {
        requestAnimationFrame(() => this.animate());

        const currentTime = performance.now();
        const frameTime = currentTime - this.lastFrameTime;
        this.lastFrameTime = currentTime;

        const fps = Math.round(1000 / frameTime);

        // Update performance stats
        this.updatePerformanceStats(fps, frameTime);

        // Update stats every 30 frames
        if (!this.frameCount) this.frameCount = 0;
        this.frameCount++;

        if (this.frameCount % 30 === 0) {
          this.updateStats();
        }

        // Rotate planet slowly
        if (this.planetMesh) {
          this.planetMesh.rotation.y += 0.002;
        }

        // Rotate atmosphere layers at different speeds
        if (this.atmosphereMesh) {
          this.atmosphereMesh.rotation.y += 0.001;
        }

        if (this.stratosphereMesh) {
          this.stratosphereMesh.rotation.y -= 0.0005;
        }

        // Animate clouds
        this.cloudMeshes.forEach((mesh, index) => {
          mesh.rotation.y += 0.0003 * (index + 1);
        });

        this.renderer.render(this.scene, this.camera);
      }
    }

    // Initialize the system when page loads
    window.addEventListener('load', () => {
      new AdvancedPlanetarySystem();
    });

    console.log('Advanced Planetary Terrain Demo - Complete System Loaded');
  </script>
</body>
</html>
