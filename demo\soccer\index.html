<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Football Sim – Modular Prototype</title>
    <style>
        :root {
            --bg-color: #222;
            --field-color: #065;
        }
        body {
            margin: 0; padding: 0; background: var(--bg-color); color: #fff;
            font-family: Arial, sans-serif;
            min-height: 100vh;
        }
        #toolbar {
            display: flex;
            gap: 2em;
            align-items: center;
            justify-content: center;
            padding: 18px 0 8px 0;
            background: #191919;
        }
        label, select { font-size: 1.1em; }
        canvas {
            display: block;
            margin: 0 auto 30px auto;
            background-color: #4CAF50;
            border: 2px solid white;
            box-shadow: 0 4px 20px #0008;
        }

        #commentary {
            width: 1050px;
            margin: 0 auto 20px auto;
            background: #111;
            padding: 6px 10px;
            font-family: monospace;
            min-height: 40px;
        }

        #scoreboard {
  text-align: center;
  font-size: 1.7em;
  padding: 12px 0 12px 0;
  background: #282828;
  color: #fff;
  letter-spacing: 0.04em;
  border-bottom: 1.5px solid #444;
}
#scoreboard span { margin: 0 0.3em; }
        #powerBarWrapper {
            width: 150px;
            height: 10px;
            background: #333;
            margin: 6px auto;
            display: none;
        }
        #powerBar {
            height: 100%;
            width: 0%;
            background: linear-gradient(green, red);
        }
        #radar {
            display: block;
            margin: 6px auto;
            background: var(--field-color);
            border: 2px solid white;
        }
    </style>
</head>
<body>
    <div id="toolbar">
        <label for="formationSelect">Formation:</label>
        <select id="formationSelect"></select>
        <span id="formationDesc"></span>
        <label for="halfLengthInput">Halbzeitlänge (Minuten):</label>
        <input id="halfLengthInput" type="number" value="45" min="1" max="60">
        <label for="difficultySelect">Schwierigkeitsgrad:</label>
        <select id="difficultySelect">
            <option value="easy">Leicht</option>
            <option value="normal" selected>Normal</option>
            <option value="hard">Schwer</option>
        </select>
        <label for="weatherSelect">Wetter:</label>
        <select id="weatherSelect">
            <option value="clear" selected>Trocken</option>
            <option value="rain">Regen</option>
            <option value="wind">Windig</option>
        </select>
        <div id="analysisPanel" style="display: none; padding: 12px; background: #111; color: #eee; font-size: 0.9em;">
  <h3>Spieleranalyse</h3>
  <button onclick="document.getElementById('analysisPanel').style.display='none'">✖ schließen</button>
  <div id="analysisTable"></div>
</div>

<style>
  #analysisPanel table {
    width: 100%;
    border-collapse: collapse;
  }
  #analysisPanel th, #analysisPanel td {
    border: 1px solid #555;
    padding: 4px 8px;
    text-align: left;
  }
  #analysisPanel th {
    background: #222;
  }
</style>

    </div>

    <div id="scoreboard">
  <span id="score">0 : 0</span> &nbsp; | &nbsp;
  <span id="timer">00:00</span> &nbsp; | &nbsp;
  <span id="halftime">1. Halbzeit</span> &nbsp; | &nbsp;
  <span id="cards"></span>
</div>
    <canvas id="radar" width="210" height="136"></canvas>
    <div id="powerBarWrapper"><div id="powerBar"></div></div>
    <p style="text-align:center;margin-top:4px;">Spieler anklicken und mit Pfeiltasten oder Gamepad steuern &ndash; 'R' setzt das Spiel zurück</p>
    <div style="position:relative;width:1050px;margin:0 auto;">
      <canvas id="spielfeld" width="1050" height="680"></canvas>
      <canvas id="debugCanvas" width="1050" height="680" style="position:absolute;top:0;left:0;pointer-events:none;background:transparent;z-index:10;"></canvas>
      <div id="inspector" style="position:absolute;pointer-events:none;color:#fff;background:#000a;padding:4px;font-size:12px;display:none;"></div>
    </div>
    <div id="commentary"></div>
    <script type="module" src="main.js"></script>
</body>
</html>
