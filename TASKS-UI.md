# UI Control Panel Tasks

This document outlines the proposed features for a central command center in the soccer demo. The panel can be rendered inside the canvas or slide in from the side to provide quick access to debugging and configuration options.

## Modules and Tabs

- [x] **Input Mapping**: View and modify keybinds (e.g. Pass = Space, Cancel = Backspace, Shot = S, Lob = Shift).
- [x] **Player Stats**: Display player attributes such as Speed, Technique and Vision with filters for team and position.
- [x] **Formation**: Show the team's formation graphically and allow potential changes.
- [x] **Pitch Info**: Present field properties like dimensions, grass type, friction and optional weather.
- [x] **Debugging**: Visualise current states such as player zones, vision cones, ball vectors and decisions (e.g. "tackle prepared").
- [x] **Rendering Options**: Switch colour profiles, line transparency and debug overlays on or off.
- [x] **Live Data**: Provide a JSON snapshot of the world, players and match state with download capability.
- [x] **Log / Console**: List recent events, e.g. "tackle executed" or "lost possession".
