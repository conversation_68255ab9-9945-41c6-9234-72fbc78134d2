<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced Planetary System - Compatible Version</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    .parameter-slider {
      width: 100%;
      height: 8px;
      background: #e5e7eb;
      border-radius: 4px;
      appearance: none;
      cursor: pointer;
    }
    .parameter-slider::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: #3b82f6;
      border-radius: 50%;
      cursor: pointer;
    }
    .feature-toggle {
      @apply px-3 py-1 text-xs rounded transition-colors cursor-pointer;
    }
    .feature-toggle.active {
      @apply bg-blue-600 text-white;
    }
    .feature-toggle.inactive {
      @apply bg-gray-600 text-gray-300;
    }
  </style>
</head>
<body class="bg-gray-900 text-white">
  <div class="min-h-screen p-4">
    <!-- Header -->
    <div class="max-w-7xl mx-auto mb-6">
      <h1 class="text-4xl font-bold text-center mb-2">🌍 Enhanced Planetary System</h1>
      <p class="text-center text-gray-400">Advanced geological, atmospheric, and hydrological features</p>
    </div>

    <div class="max-w-7xl mx-auto grid grid-cols-1 xl:grid-cols-5 gap-4">

      <!-- Controls Panel -->
      <div class="xl:col-span-1 space-y-3 max-h-screen overflow-y-auto pr-2">

        <!-- Quick Actions -->
        <div class="bg-gray-800 p-3 rounded-lg border border-gray-700">
          <div class="grid grid-cols-2 gap-2">
            <button id="quick-regenerate" class="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm font-medium transition-colors">
              🔄 Regenerate
            </button>
            <button id="quick-random" class="bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded text-sm font-medium transition-colors">
              🎲 Random
            </button>
            <button id="cancel-generation" class="bg-red-600 hover:bg-red-700 px-3 py-2 rounded text-sm font-medium transition-colors" style="display: none;">
              ⏹️ Cancel
            </button>
            <button id="pause-generation" class="bg-yellow-600 hover:bg-yellow-700 px-3 py-2 rounded text-sm font-medium transition-colors" style="display: none;">
              ⏸️ Pause
            </button>
          </div>
        </div>
        
        <!-- Tectonic Controls -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🌋 Tectonic System</h3>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Plate Activity</label>
              <input type="range" id="tectonic-activity" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Stable</span>
                <span id="tectonic-value" class="font-mono text-blue-400">1.0</span>
                <span>Violent</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Mountain Formation</label>
              <input type="range" id="mountain-formation" min="0" max="2" step="0.1" value="1.2" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Flat</span>
                <span id="mountain-value" class="font-mono text-blue-400">1.2</span>
                <span>Extreme</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Volcanic Activity</label>
              <input type="range" id="volcanic-activity" min="0" max="2" step="0.1" value="0.8" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="volcanic-value" class="font-mono text-blue-400">0.8</span>
                <span>Extreme</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-2 mt-4">
              <button id="toggle-boundaries" class="feature-toggle active">Boundaries</button>
              <button id="toggle-hotspots" class="feature-toggle active">Hotspots</button>
            </div>
          </div>
        </div>

        <!-- Climate Controls -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🌡️ Climate System</h3>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Global Temperature</label>
              <input type="range" id="global-temp" min="200" max="350" value="288" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>200K</span>
                <span id="temp-value" class="font-mono text-blue-400">288K</span>
                <span>350K</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Precipitation</label>
              <input type="range" id="precipitation" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Arid</span>
                <span id="precip-value" class="font-mono text-blue-400">1.0</span>
                <span>Wet</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Ocean Coverage</label>
              <input type="range" id="ocean-coverage" min="0" max="1" step="0.01" value="0.71" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>0%</span>
                <span id="ocean-value" class="font-mono text-blue-400">71%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Atmospheric Controls -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🌫️ Atmosphere</h3>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Atmospheric Pressure</label>
              <input type="range" id="atm-pressure" min="0" max="3" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="pressure-value" class="font-mono text-blue-400">1.0</span>
                <span>Dense</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Cloud Coverage</label>
              <input type="range" id="cloud-coverage" min="0" max="1" step="0.01" value="0.6" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Clear</span>
                <span id="cloud-value" class="font-mono text-blue-400">60%</span>
                <span>Overcast</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Seasonal Cycle</label>
              <input type="range" id="seasonal-cycle" min="0" max="1" step="0.01" value="0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Spring</span>
                <span id="season-value" class="font-mono text-blue-400">Spring</span>
                <span>Winter</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Atmospheric Scattering</label>
              <input type="range" id="atmospheric-scattering" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="scattering-value" class="font-mono text-blue-400">1.0x</span>
                <span>Intense</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Cloud Dynamics</label>
              <input type="range" id="cloud-dynamics" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Static</span>
                <span id="dynamics-value" class="font-mono text-blue-400">1.0x</span>
                <span>Turbulent</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-2 mt-4">
              <button id="toggle-atmosphere" class="feature-toggle active">Atmosphere</button>
              <button id="toggle-clouds" class="feature-toggle active">Clouds</button>
              <button id="toggle-volumetric" class="feature-toggle active">Volumetric</button>
              <button id="toggle-shadows" class="feature-toggle active">Shadows</button>
            </div>
          </div>
        </div>

        <!-- Terrain Controls -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🏔️ Terrain</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Planet Seed</label>
              <input type="text" id="planet-seed" value="12345"
                     class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm">
              <button id="random-seed" class="mt-1 px-2 py-1 bg-purple-600 hover:bg-purple-700 rounded text-xs">
                Random Seed
              </button>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Base Detail Level</label>
              <input type="range" id="terrain-detail" min="4" max="8" value="6" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Low</span>
                <span id="detail-level" class="font-mono text-blue-400">High</span>
                <span>Ultra</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">LOD Max Detail</label>
              <input type="range" id="lod-max-detail" min="6" max="10" value="8" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Standard</span>
                <span id="lod-max-value" class="font-mono text-green-400">8</span>
                <span>Extreme</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">LOD Distance</label>
              <input type="range" id="lod-distance" min="2" max="10" step="0.5" value="5" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Close</span>
                <span id="lod-distance-value" class="font-mono text-green-400">5.0</span>
                <span>Far</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Surface Detail</label>
              <input type="range" id="surface-detail" min="0" max="3" step="0.1" value="1.5" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Smooth</span>
                <span id="surface-detail-value" class="font-mono text-orange-400">1.5x</span>
                <span>Rough</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Erosion Rate</label>
              <input type="range" id="erosion-rate" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="erosion-value" class="font-mono text-blue-400">1.0</span>
                <span>Heavy</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-2 mt-4">
              <button id="toggle-wireframe" class="feature-toggle inactive">Wireframe</button>
              <button id="regenerate" class="feature-toggle active">Regenerate</button>
              <button id="toggle-lod" class="feature-toggle active">Dynamic LOD</button>
              <button id="toggle-adaptive" class="feature-toggle active">Adaptive</button>
            </div>
          </div>
        </div>

        <!-- Performance Stats -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">⚡ Performance Analytics</h3>

          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>FPS:</span>
              <span id="fps-counter" class="font-mono text-green-400">60</span>
            </div>
            <div class="flex justify-between">
              <span>Frame Time:</span>
              <span id="frame-time" class="font-mono text-blue-400">16.7ms</span>
            </div>
            <div class="flex justify-between">
              <span>Vertices:</span>
              <span id="vertex-count" class="font-mono text-blue-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Triangles:</span>
              <span id="triangle-count" class="font-mono text-blue-400">0</span>
            </div>
            <div class="flex justify-between">
              <span>Draw Calls:</span>
              <span id="draw-calls" class="font-mono text-purple-400">3</span>
            </div>
            <div class="flex justify-between">
              <span>Memory:</span>
              <span id="memory-usage" class="font-mono text-yellow-400">0MB</span>
            </div>
          </div>

          <!-- Performance Graph -->
          <div class="mt-3">
            <div class="text-xs text-gray-400 mb-1">FPS History</div>
            <canvas id="fps-graph" width="200" height="40" class="w-full h-10 bg-gray-900 rounded"></canvas>
          </div>
        </div>

        <!-- Advanced Controls -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🎛️ Advanced Controls</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Geological Time Scale</label>
              <input type="range" id="geological-time" min="0" max="100" value="50" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Young</span>
                <span id="geological-value" class="font-mono text-blue-400">50 Myr</span>
                <span>Ancient</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Magnetic Field Strength</label>
              <input type="range" id="magnetic-field" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="magnetic-value" class="font-mono text-blue-400">1.0x</span>
                <span>Strong</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Rotation Speed</label>
              <input type="range" id="rotation-speed" min="0.1" max="5" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Slow</span>
                <span id="rotation-value" class="font-mono text-blue-400">1.0x</span>
                <span>Fast</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-2 mt-4">
              <button id="toggle-aurora" class="feature-toggle inactive">Aurora</button>
              <button id="toggle-rings" class="feature-toggle inactive">Rings</button>
              <button id="toggle-magnetic-field" class="feature-toggle inactive">Mag Field</button>
              <button id="auto-rotate" class="feature-toggle active">Auto Rotate</button>
            </div>
          </div>
        </div>

        <!-- Planet Analysis -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🔬 Planet Analysis</h3>

          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>Planet Age:</span>
              <span id="planet-age" class="font-mono text-blue-400">50 Myr</span>
            </div>
            <div class="flex justify-between">
              <span>Habitability:</span>
              <span id="habitability-score" class="font-mono text-green-400">0.75</span>
            </div>
            <div class="flex justify-between">
              <span>Geological Activity:</span>
              <span id="geological-activity" class="font-mono text-orange-400">Moderate</span>
            </div>
            <div class="flex justify-between">
              <span>Atmospheric Density:</span>
              <span id="atmospheric-density" class="font-mono text-purple-400">1.0 atm</span>
            </div>
            <div class="flex justify-between">
              <span>Surface Gravity:</span>
              <span id="surface-gravity" class="font-mono text-yellow-400">9.8 m/s²</span>
            </div>
            <div class="flex justify-between">
              <span>Magnetic Protection:</span>
              <span id="magnetic-protection" class="font-mono text-cyan-400">Strong</span>
            </div>
          </div>

          <!-- Habitability Meter -->
          <div class="mt-3">
            <div class="text-xs text-gray-400 mb-1">Habitability Index</div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div id="habitability-bar" class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full transition-all duration-300" style="width: 75%"></div>
            </div>
          </div>
        </div>

        <!-- Atmospheric Composition -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🧪 Atmospheric Composition</h3>

          <div class="space-y-2 text-xs">
            <div class="flex justify-between items-center">
              <span>Nitrogen (N₂):</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-700 rounded-full h-1 mr-2">
                  <div id="nitrogen-bar" class="bg-blue-400 h-1 rounded-full" style="width: 78%"></div>
                </div>
                <span id="nitrogen-percent" class="font-mono text-blue-400 w-8">78%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Oxygen (O₂):</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-700 rounded-full h-1 mr-2">
                  <div id="oxygen-bar" class="bg-green-400 h-1 rounded-full" style="width: 21%"></div>
                </div>
                <span id="oxygen-percent" class="font-mono text-green-400 w-8">21%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Carbon Dioxide (CO₂):</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-700 rounded-full h-1 mr-2">
                  <div id="co2-bar" class="bg-red-400 h-1 rounded-full" style="width: 0.04%"></div>
                </div>
                <span id="co2-percent" class="font-mono text-red-400 w-8">0.04%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Water Vapor (H₂O):</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-700 rounded-full h-1 mr-2">
                  <div id="water-bar" class="bg-cyan-400 h-1 rounded-full" style="width: 1%"></div>
                </div>
                <span id="water-percent" class="font-mono text-cyan-400 w-8">1%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Trace Gases:</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-700 rounded-full h-1 mr-2">
                  <div id="trace-bar" class="bg-purple-400 h-1 rounded-full" style="width: 0.96%"></div>
                </div>
                <span id="trace-percent" class="font-mono text-purple-400 w-8">0.96%</span>
              </div>
            </div>
          </div>

          <!-- Atmospheric Quality -->
          <div class="mt-3 pt-2 border-t border-gray-700">
            <div class="flex justify-between text-xs">
              <span>Breathability:</span>
              <span id="breathability" class="font-mono text-green-400">Excellent</span>
            </div>
            <div class="flex justify-between text-xs">
              <span>Greenhouse Effect:</span>
              <span id="greenhouse-effect" class="font-mono text-yellow-400">Moderate</span>
            </div>
          </div>
        </div>

        <!-- Environmental Effects -->
        <div class="bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-600">🌊 Environmental Effects</h3>

          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Ocean Currents</label>
              <input type="range" id="ocean-currents" min="0" max="2" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Still</span>
                <span id="currents-value" class="font-mono text-blue-400">1.0x</span>
                <span>Turbulent</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Lightning Activity</label>
              <input type="range" id="lightning-activity" min="0" max="3" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>None</span>
                <span id="lightning-value" class="font-mono text-yellow-400">1.0x</span>
                <span>Intense</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Particle Density</label>
              <input type="range" id="particle-density" min="0" max="3" step="0.1" value="1.0" class="parameter-slider">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>Clear</span>
                <span id="particle-value" class="font-mono text-orange-400">1.0x</span>
                <span>Dense</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-2 mt-4">
              <button id="toggle-lightning" class="feature-toggle inactive">Lightning</button>
              <button id="toggle-particles" class="feature-toggle active">Particles</button>
              <button id="toggle-currents" class="feature-toggle active">Currents</button>
              <button id="toggle-effects" class="feature-toggle active">Effects</button>
            </div>
          </div>
        </div>
          </div>
        </div>
      </div>

      <!-- 3D Visualization -->
      <div class="xl:col-span-3">
        <div class="bg-gray-800 p-3 rounded-lg border border-gray-700 shadow-lg">
          <div class="relative">
            <canvas id="planet-canvas" class="w-full h-80 lg:h-96 xl:h-[600px] border border-gray-600 rounded bg-black"></canvas>
            
            <!-- Controls Overlay -->
            <div class="absolute top-2 left-2 bg-black bg-opacity-75 text-white p-2 rounded text-xs">
              <div>🖱️ Drag: Rotate Planet</div>
              <div>🔍 Wheel: Zoom (0.5x - 20x)</div>
              <div>⌨️ Space: Reset View</div>
              <div class="text-green-400">⭐ Enhanced Features:</div>
              <div class="text-yellow-400">• Realistic Lighting & Shadows</div>
              <div class="text-blue-400">• Natural Terrain Variation</div>
              <div class="text-purple-400">• Deterministic Seed System</div>
            </div>
            
            <!-- Planet Info -->
            <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white p-2 rounded text-sm">
              <div id="planet-name" class="font-bold">Enhanced Planet</div>
              <div class="text-xs opacity-75">Advanced Geological Simulation</div>
              <div id="planet-type" class="text-xs text-blue-400">Terrestrial World</div>
            </div>

            <!-- Progress Overlay -->
            <div id="progress-overlay" class="absolute inset-0 bg-black bg-opacity-80 flex items-center justify-center hidden">
              <div class="bg-gray-800 p-6 rounded-lg max-w-md w-full mx-4">
                <div class="text-center mb-4">
                  <h3 class="text-xl font-bold text-white mb-2">🌍 Generating Planet</h3>
                  <div id="progress-stage" class="text-blue-400 text-sm">Initializing...</div>
                </div>

                <!-- Progress Bar -->
                <div class="w-full bg-gray-700 rounded-full h-3 mb-4">
                  <div id="progress-bar" class="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>

                <!-- Progress Details -->
                <div class="space-y-2 text-xs">
                  <div class="flex justify-between">
                    <span class="text-gray-400">Overall Progress:</span>
                    <span id="progress-percent" class="text-white font-mono">0%</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Current Step:</span>
                    <span id="progress-step" class="text-green-400">1/8</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Vertices:</span>
                    <span id="progress-vertices" class="text-blue-400">0</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-400">Elapsed Time:</span>
                    <span id="progress-time" class="text-yellow-400">0.0s</span>
                  </div>
                </div>

                <!-- Current Process Details -->
                <div class="mt-4 p-3 bg-gray-700 rounded text-xs">
                  <div class="text-gray-300 mb-1">Current Process:</div>
                  <div id="progress-details" class="text-white">Preparing generation systems...</div>
                </div>

                <!-- Cancel Button -->
                <div class="mt-4 text-center">
                  <button id="cancel-generation" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                    Cancel Generation
                  </button>
                </div>

                <!-- Process List -->
                <div class="mt-4 space-y-1 text-xs">
                  <div id="step-geometry" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Generate Base Geometry</span>
                  </div>
                  <div id="step-tectonics" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Apply Tectonic Processes</span>
                  </div>
                  <div id="step-climate" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Calculate Climate Effects</span>
                  </div>
                  <div id="step-erosion" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Apply Erosion Modeling</span>
                  </div>
                  <div id="step-atmosphere" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Generate Atmosphere</span>
                  </div>
                  <div id="step-weather" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Create Weather Systems</span>
                  </div>
                  <div id="step-clouds" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Generate Cloud Patterns</span>
                  </div>
                  <div id="step-finalize" class="flex items-center text-gray-400">
                    <span class="w-4 h-4 mr-2">⏳</span>
                    <span>Finalize & Optimize</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Layer Controls -->
            <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white p-2 rounded text-xs">
              <div class="grid grid-cols-3 gap-1">
                <button id="layer-surface" class="feature-toggle active">Surface</button>
                <button id="layer-elevation" class="feature-toggle inactive">Elevation</button>
                <button id="layer-temperature" class="feature-toggle inactive">Temperature</button>
                <button id="layer-biomes" class="feature-toggle inactive">Biomes</button>
                <button id="layer-tectonics" class="feature-toggle inactive">Tectonics</button>
                <button id="layer-weather" class="feature-toggle inactive">Weather</button>
              </div>
            </div>
          </div>

          <!-- Feature Summary -->
          <div class="mt-4 grid grid-cols-2 lg:grid-cols-4 gap-2">
            <div class="bg-gray-700 p-2 rounded text-center">
              <div class="text-xs text-gray-400">Tectonic Plates</div>
              <div id="plate-count" class="font-bold text-orange-400">7</div>
            </div>
            <div class="bg-gray-700 p-2 rounded text-center">
              <div class="text-xs text-gray-400">Active Biomes</div>
              <div id="biome-count" class="font-bold text-green-400">9</div>
            </div>
            <div class="bg-gray-700 p-2 rounded text-center">
              <div class="text-xs text-gray-400">Weather Systems</div>
              <div id="weather-count" class="font-bold text-blue-400">6</div>
            </div>
            <div class="bg-gray-700 p-2 rounded text-center">
              <div class="text-xs text-gray-400">Cyclones Active</div>
              <div id="cyclone-count" class="font-bold text-red-400">3</div>
            </div>
          </div>

          <!-- Advanced Weather Info -->
          <div class="mt-4 bg-gray-700 p-3 rounded">
            <h4 class="text-sm font-semibold mb-2">🌪️ Dynamic Weather</h4>
            <div class="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span class="text-gray-400">Seasonal Phase:</span>
                <span id="current-season" class="text-blue-400 ml-1">Spring</span>
              </div>
              <div>
                <span class="text-gray-400">Weather Time:</span>
                <span id="weather-time" class="text-green-400 ml-1">0.0s</span>
              </div>
              <div>
                <span class="text-gray-400">Storm Activity:</span>
                <span id="storm-activity" class="text-yellow-400 ml-1">Moderate</span>
              </div>
              <div>
                <span class="text-gray-400">Cloud Coverage:</span>
                <span id="current-clouds" class="text-cyan-400 ml-1">60%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Enhanced Noise Library with Deterministic Distortion
    class EnhancedNoise {
      static hash(x, y, z, seed = 0) {
        let h = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + seed * 43.758) * 43758.5453;
        return h - Math.floor(h);
      }

      static hash2D(x, y, seed = 0) {
        let h = Math.sin(x * 12.9898 + y * 78.233 + seed * 43.758) * 43758.5453;
        return h - Math.floor(h);
      }

      static perlin3D(x, y, z, seed = 0) {
        const ix = Math.floor(x);
        const iy = Math.floor(y);
        const iz = Math.floor(z);
        const fx = x - ix;
        const fy = y - iy;
        const fz = z - iz;

        const u = fx * fx * (3 - 2 * fx);
        const v = fy * fy * (3 - 2 * fy);
        const w = fz * fz * (3 - 2 * fz);

        const c000 = this.hash(ix, iy, iz, seed);
        const c100 = this.hash(ix + 1, iy, iz, seed);
        const c010 = this.hash(ix, iy + 1, iz, seed);
        const c110 = this.hash(ix + 1, iy + 1, iz, seed);
        const c001 = this.hash(ix, iy, iz + 1, seed);
        const c101 = this.hash(ix + 1, iy, iz + 1, seed);
        const c011 = this.hash(ix, iy + 1, iz + 1, seed);
        const c111 = this.hash(ix + 1, iy + 1, iz + 1, seed);

        const x1 = c000 * (1 - u) + c100 * u;
        const x2 = c010 * (1 - u) + c110 * u;
        const x3 = c001 * (1 - u) + c101 * u;
        const x4 = c011 * (1 - u) + c111 * u;

        const y1 = x1 * (1 - v) + x2 * v;
        const y2 = x3 * (1 - v) + x4 * v;

        return y1 * (1 - w) + y2 * w;
      }

      // Domain warping for natural variation
      static domainWarp(x, y, z, seed, strength = 1.0) {
        const warpX = this.perlin3D(x * 0.3, y * 0.3, z * 0.3, seed + 1000) * strength;
        const warpY = this.perlin3D(x * 0.3, y * 0.3, z * 0.3, seed + 2000) * strength;
        const warpZ = this.perlin3D(x * 0.3, y * 0.3, z * 0.3, seed + 3000) * strength;

        return {
          x: x + warpX,
          y: y + warpY,
          z: z + warpZ
        };
      }

      // Distorted noise with seed-based variation
      static distortedNoise(x, y, z, seed, warpStrength = 0.5) {
        const warped = this.domainWarp(x, y, z, seed, warpStrength);
        return this.perlin3D(warped.x, warped.y, warped.z, seed);
      }

      static fractal(x, y, z, octaves, persistence = 0.5, seed = 0, warpStrength = 0.3) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;

        // Apply domain warping for more natural variation
        const warped = this.domainWarp(x, y, z, seed, warpStrength);

        for (let i = 0; i < octaves; i++) {
          // Use different seed offsets for each octave to create variation
          const octaveSeed = seed + i * 1000;
          const octaveWarp = this.domainWarp(warped.x, warped.y, warped.z, octaveSeed, warpStrength * 0.5);

          value += this.perlin3D(
            octaveWarp.x * frequency,
            octaveWarp.y * frequency,
            octaveWarp.z * frequency,
            octaveSeed
          ) * amplitude;

          maxValue += amplitude;
          amplitude *= persistence;
          frequency *= 2.0 + (this.hash2D(seed, i) - 0.5) * 0.2; // Slight frequency variation
        }

        return value / maxValue;
      }

      static ridged(x, y, z, octaves, seed = 0, warpStrength = 0.4) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;

        // Apply domain warping for more natural ridges
        const warped = this.domainWarp(x, y, z, seed, warpStrength);

        for (let i = 0; i < octaves; i++) {
          const octaveSeed = seed + i * 1500;
          const octaveWarp = this.domainWarp(warped.x, warped.y, warped.z, octaveSeed, warpStrength * 0.3);

          const n = Math.abs(this.perlin3D(
            octaveWarp.x * frequency,
            octaveWarp.y * frequency,
            octaveWarp.z * frequency,
            octaveSeed
          ));

          // Create sharper ridges with power function
          const ridge = Math.pow(1 - n, 2.0);
          value += ridge * amplitude;

          amplitude *= 0.5;
          frequency *= 2.0 + (this.hash2D(seed + 100, i) - 0.5) * 0.3;
        }

        return value;
      }

      static worley(x, y, z, seed = 0, warpStrength = 0.2) {
        // Apply subtle domain warping to Worley noise
        const warped = this.domainWarp(x, y, z, seed, warpStrength);

        const ix = Math.floor(warped.x);
        const iy = Math.floor(warped.y);
        const iz = Math.floor(warped.z);

        let minDist = Infinity;
        let secondMinDist = Infinity;

        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            for (let dz = -1; dz <= 1; dz++) {
              const cellX = ix + dx;
              const cellY = iy + dy;
              const cellZ = iz + dz;

              // Add seed-based variation to cell point positions
              const pointX = cellX + this.hash(cellX, cellY, cellZ, seed) * 0.8 + 0.1;
              const pointY = cellY + this.hash(cellX, cellY, cellZ, seed + 1) * 0.8 + 0.1;
              const pointZ = cellZ + this.hash(cellX, cellY, cellZ, seed + 2) * 0.8 + 0.1;

              const dist = Math.sqrt(
                (warped.x - pointX) * (warped.x - pointX) +
                (warped.y - pointY) * (warped.y - pointY) +
                (warped.z - pointZ) * (warped.z - pointZ)
              );

              if (dist < minDist) {
                secondMinDist = minDist;
                minDist = dist;
              } else if (dist < secondMinDist) {
                secondMinDist = dist;
              }
            }
          }
        }

        // Return F2 - F1 for more interesting patterns
        return secondMinDist - minDist;
      }

      // Turbulence with domain warping
      static turbulence(x, y, z, octaves, seed = 0, warpStrength = 0.5) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;

        const warped = this.domainWarp(x, y, z, seed, warpStrength);

        for (let i = 0; i < octaves; i++) {
          const octaveSeed = seed + i * 2000;
          const octaveWarp = this.domainWarp(warped.x, warped.y, warped.z, octaveSeed, warpStrength * 0.2);

          value += Math.abs(this.perlin3D(
            octaveWarp.x * frequency,
            octaveWarp.y * frequency,
            octaveWarp.z * frequency,
            octaveSeed
          )) * amplitude;

          amplitude *= 0.5;
          frequency *= 2.0;
        }

        return value;
      }

      // Billowy noise for cloud-like formations
      static billowy(x, y, z, octaves, seed = 0, warpStrength = 0.6) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;

        const warped = this.domainWarp(x, y, z, seed, warpStrength);

        for (let i = 0; i < octaves; i++) {
          const octaveSeed = seed + i * 2500;
          const octaveWarp = this.domainWarp(warped.x, warped.y, warped.z, octaveSeed, warpStrength * 0.3);

          const n = this.perlin3D(
            octaveWarp.x * frequency,
            octaveWarp.y * frequency,
            octaveWarp.z * frequency,
            octaveSeed
          );

          // Create billowy effect by taking absolute value and subtracting
          value += (Math.abs(n) * 2 - 1) * amplitude;
          maxValue += amplitude;
          amplitude *= 0.5;
          frequency *= 2.0;
        }

        return value / maxValue;
      }
    }

    // Enhanced Planetary System
    class EnhancedPlanetarySystem {
      constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.planetMesh = null;
        this.atmosphereMesh = null;
        this.cloudMesh = null;
        this.animationId = null;

        // System parameters
        this.tectonicActivity = 1.0;
        this.mountainFormation = 1.2;
        this.volcanicActivity = 0.8;
        this.globalTemp = 288;
        this.precipitation = 1.0;
        this.oceanCoverage = 0.71;
        this.atmPressure = 1.0;
        this.cloudCoverage = 0.6;
        this.terrainDetail = 6;
        this.lodMaxDetail = 8;
        this.lodDistance = 5.0;
        this.surfaceDetail = 1.5;
        this.erosionRate = 1.0;
        this.wireframeMode = false;

        // LOD system
        this.lodMeshes = [];
        this.lodLevels = [];
        this.adaptiveSubdivision = true;
        this.dynamicLOD = true;

        // Features
        this.features = {
          boundaries: true,
          hotspots: true,
          atmosphere: true,
          clouds: true
        };

        // Current layer
        this.currentLayer = 'surface';

        // Dynamic weather system
        this.weatherTime = 0;
        this.seasonalCycle = 0;
        this.weatherSystems = [];

        // Progress tracking
        this.isGenerating = false;
        this.generationCancelled = false;
        this.generationPaused = false;
        this.generationStartTime = 0;
        this.currentStep = 0;
        this.totalSteps = 12;

        // Async generation control
        this.asyncChunkSize = 1000; // Process this many vertices per frame
        this.asyncDelay = 16; // Minimum delay between chunks (ms)
        this.lastAsyncTime = 0;

        // Advanced features
        this.geologicalTime = 50; // Million years
        this.magneticField = 1.0;
        this.rotationSpeed = 1.0;
        this.autoRotate = true;
        this.atmosphericScattering = 1.0;
        this.cloudDynamics = 1.0;
        this.oceanCurrents = 1.0;
        this.lightningActivity = 1.0;
        this.particleDensity = 1.0;
        this.features.aurora = false;
        this.features.rings = false;
        this.features.magneticField = false;
        this.features.volumetric = true;
        this.features.shadows = true;
        this.features.lightning = false;
        this.features.particles = true;
        this.features.currents = true;
        this.features.effects = true;

        // Atmospheric composition
        this.atmosphericComposition = {
          nitrogen: 78.0,
          oxygen: 21.0,
          co2: 0.04,
          water: 1.0,
          trace: 0.96
        };

        // Environmental systems
        this.lightningStrikes = [];
        this.environmentalParticles = [];
        this.oceanCurrentSystem = null;

        // Selective regeneration tracking
        this.regenerationFlags = {
          geometry: false,
          tectonics: false,
          climate: false,
          atmosphere: false,
          clouds: false,
          weather: false,
          features: false
        };

        // Cache for expensive calculations
        this.geometryCache = null;
        this.lastParameters = {};

        // Performance tracking
        this.fpsHistory = [];
        this.frameTimeHistory = [];
        this.performanceStats = {
          drawCalls: 0,
          memoryUsage: 0,
          lastFrameTime: 0
        };

        this.seed = 12345;

        this.initialize3D();
        this.initializeControls();
        this.initializePerformanceTracking();
        this.generateWeatherSystems();

        // Generate initial planet asynchronously
        setTimeout(() => {
          this.generatePlanet();
        }, 100);

        this.animate();
      }

      initialize3D() {
        const canvas = document.getElementById('planet-canvas');

        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.01, 1000);
        this.camera.position.set(0, 0, 3);

        // Renderer setup with enhanced settings
        this.renderer = new THREE.WebGLRenderer({
          canvas: canvas,
          antialias: true,
          powerPreference: "high-performance"
        });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

        // Enhanced shadow settings
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.shadowMap.autoUpdate = true;

        // Enhanced rendering settings
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        this.renderer.outputEncoding = THREE.sRGBEncoding;

        // Enable physically correct lighting
        this.renderer.physicallyCorrectLights = true;

        // Lighting
        this.setupLighting();

        // Starfield
        this.createStarfield();

        // Controls
        this.setupControls();

        // Handle resize
        window.addEventListener('resize', () => this.onWindowResize());
      }

      setupLighting() {
        // Ambient light (reduced for more dramatic shadows)
        const ambientLight = new THREE.AmbientLight(0x404040, 0.15);
        this.scene.add(ambientLight);

        // Main sun light with enhanced shadows
        this.sunLight = new THREE.DirectionalLight(0xfff8e1, 2.5);
        this.sunLight.position.set(15, 8, 10);
        this.sunLight.castShadow = true;

        // High-resolution shadow mapping
        this.sunLight.shadow.mapSize.width = 4096;
        this.sunLight.shadow.mapSize.height = 4096;
        this.sunLight.shadow.camera.near = 0.1;
        this.sunLight.shadow.camera.far = 50;
        this.sunLight.shadow.camera.left = -5;
        this.sunLight.shadow.camera.right = 5;
        this.sunLight.shadow.camera.top = 5;
        this.sunLight.shadow.camera.bottom = -5;
        this.sunLight.shadow.bias = -0.0001;
        this.sunLight.shadow.normalBias = 0.02;

        this.scene.add(this.sunLight);

        // Atmospheric fill light (cooler tone)
        const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.6);
        fillLight.position.set(-8, 4, -12);
        fillLight.castShadow = false; // Only main light casts shadows
        this.scene.add(fillLight);

        // Rim light for atmospheric edge lighting
        const rimLight = new THREE.DirectionalLight(0xffffff, 0.3);
        rimLight.position.set(0, 0, -20);
        this.scene.add(rimLight);

        // Subsurface scattering simulation (warm bounce light)
        const bounceLight = new THREE.DirectionalLight(0xffaa77, 0.4);
        bounceLight.position.set(-10, -5, 8);
        this.scene.add(bounceLight);
      }

      createStarfield() {
        const starsGeometry = new THREE.BufferGeometry();
        const starsMaterial = new THREE.PointsMaterial({
          color: 0xffffff,
          size: 1,
          sizeAttenuation: false
        });

        const starsVertices = [];
        for (let i = 0; i < 3000; i++) {
          const x = (Math.random() - 0.5) * 2000;
          const y = (Math.random() - 0.5) * 2000;
          const z = (Math.random() - 0.5) * 2000;
          starsVertices.push(x, y, z);
        }

        starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
        const starField = new THREE.Points(starsGeometry, starsMaterial);
        this.scene.add(starField);
      }

      setupControls() {
        const canvas = document.getElementById('planet-canvas');
        let isMouseDown = false;
        let mouseX = 0;
        let mouseY = 0;
        let rotationX = 0;
        let rotationY = 0;

        canvas.addEventListener('mousedown', (e) => {
          isMouseDown = true;
          mouseX = e.clientX;
          mouseY = e.clientY;
        });

        canvas.addEventListener('mousemove', (e) => {
          if (!isMouseDown) return;

          const deltaX = e.clientX - mouseX;
          const deltaY = e.clientY - mouseY;

          rotationY += deltaX * 0.01;
          rotationX += deltaY * 0.01;
          rotationX = Math.max(-Math.PI/2, Math.min(Math.PI/2, rotationX));

          if (this.planetMesh) {
            this.planetMesh.rotation.y = rotationY;
            this.planetMesh.rotation.x = rotationX;
          }
          if (this.atmosphereMesh) {
            this.atmosphereMesh.rotation.y = rotationY;
            this.atmosphereMesh.rotation.x = rotationX;
          }
          if (this.cloudMesh) {
            this.cloudMesh.rotation.y = rotationY * 0.8;
            this.cloudMesh.rotation.x = rotationX * 0.8;
          }

          mouseX = e.clientX;
          mouseY = e.clientY;
        });

        canvas.addEventListener('mouseup', () => {
          isMouseDown = false;
        });

        canvas.addEventListener('wheel', (e) => {
          e.preventDefault();
          const zoom = e.deltaY * 0.001;
          this.camera.position.z = Math.max(0.5, Math.min(20, this.camera.position.z + zoom));
        });

        document.addEventListener('keydown', (e) => {
          if (e.code === 'Space') {
            e.preventDefault();
            this.resetCamera();
          }
        });
      }

      resetCamera() {
        this.camera.position.set(0, 0, 3);
        if (this.planetMesh) {
          this.planetMesh.rotation.set(0, 0, 0);
        }
        if (this.atmosphereMesh) {
          this.atmosphereMesh.rotation.set(0, 0, 0);
        }
        if (this.cloudMesh) {
          this.cloudMesh.rotation.set(0, 0, 0);
        }
      }

      hashSeed(seedString) {
        // Convert string seed to numeric hash
        let hash = 0;
        for (let i = 0; i < seedString.length; i++) {
          const char = seedString.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
      }

      showProgress() {
        this.isGenerating = true;
        this.generationCancelled = false;
        this.generationStartTime = performance.now();
        this.currentStep = 0;

        const overlay = document.getElementById('progress-overlay');
        overlay.classList.remove('hidden');

        // Setup cancel button
        document.getElementById('cancel-generation').onclick = () => {
          this.cancelGeneration();
        };

        this.updateProgress('Initializing planetary generation...', 'Preparing generation systems and parameters...');
      }

      hideProgress() {
        this.isGenerating = false;
        this.generationCancelled = false;
        const overlay = document.getElementById('progress-overlay');
        overlay.classList.add('hidden');
      }

      cancelGeneration() {
        this.generationCancelled = true;
        this.updateProgress(
          'Cancelling Generation...',
          'Stopping planet generation and cleaning up resources...'
        );
      }

      updateProgress(stage, details, stepName = null) {
        if (!this.isGenerating) return;

        const currentTime = performance.now();
        const elapsedTime = (currentTime - this.generationStartTime) / 1000;

        // Update progress elements
        document.getElementById('progress-stage').textContent = stage;
        document.getElementById('progress-details').textContent = details;
        document.getElementById('progress-time').textContent = elapsedTime.toFixed(1) + 's';

        const progressPercent = Math.floor((this.currentStep / this.totalSteps) * 100);
        document.getElementById('progress-percent').textContent = progressPercent + '%';
        document.getElementById('progress-step').textContent = `${this.currentStep}/${this.totalSteps}`;
        document.getElementById('progress-bar').style.width = progressPercent + '%';

        // Update step indicators
        if (stepName) {
          this.updateStepIndicator(stepName, 'active');
        }
      }

      updateStepIndicator(stepName, status) {
        const stepElement = document.getElementById(`step-${stepName}`);
        if (!stepElement) return;

        const icon = stepElement.querySelector('span');
        const text = stepElement.querySelector('span:last-child');

        switch (status) {
          case 'active':
            icon.textContent = '🔄';
            stepElement.className = 'flex items-center text-blue-400';
            break;
          case 'complete':
            icon.textContent = '✅';
            stepElement.className = 'flex items-center text-green-400';
            break;
          case 'error':
            icon.textContent = '❌';
            stepElement.className = 'flex items-center text-red-400';
            break;
          default:
            icon.textContent = '⏳';
            stepElement.className = 'flex items-center text-gray-400';
        }
      }

      completeStep(stepName) {
        this.updateStepIndicator(stepName, 'complete');
        this.currentStep++;
      }

      async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      }

      initializePerformanceTracking() {
        // Initialize FPS graph canvas
        this.fpsCanvas = document.getElementById('fps-graph');
        this.fpsCtx = this.fpsCanvas.getContext('2d');
        this.fpsCanvas.width = 200;
        this.fpsCanvas.height = 40;

        // Initialize performance arrays
        this.fpsHistory = new Array(100).fill(60);
        this.frameTimeHistory = new Array(100).fill(16.7);
      }

      updatePerformanceStats(fps, frameTime) {
        // Update FPS history
        this.fpsHistory.push(fps);
        if (this.fpsHistory.length > 100) {
          this.fpsHistory.shift();
        }

        // Update frame time history
        this.frameTimeHistory.push(frameTime);
        if (this.frameTimeHistory.length > 100) {
          this.frameTimeHistory.shift();
        }

        // Update UI elements
        document.getElementById('fps-counter').textContent = fps;
        document.getElementById('frame-time').textContent = frameTime.toFixed(1) + 'ms';

        // Estimate memory usage (rough approximation)
        let memoryEstimate = 0;
        if (this.planetMesh) {
          const geometry = this.planetMesh.geometry;
          memoryEstimate += geometry.attributes.position.count * 3 * 4; // positions
          memoryEstimate += geometry.attributes.position.count * 3 * 4; // normals
          memoryEstimate += geometry.attributes.position.count * 3 * 4; // colors
          if (geometry.index) {
            memoryEstimate += geometry.index.count * 4; // indices
          }
        }

        // Add texture memory estimates
        if (this.cloudMesh && this.cloudMesh.material.map) {
          memoryEstimate += 1024 * 512 * 4; // Cloud texture
        }

        this.performanceStats.memoryUsage = memoryEstimate / (1024 * 1024); // Convert to MB
        document.getElementById('memory-usage').textContent = this.performanceStats.memoryUsage.toFixed(1) + 'MB';

        // Update draw calls estimate
        let drawCalls = 0;
        if (this.planetMesh) drawCalls++;
        if (this.atmosphereMesh && this.atmosphereMesh.visible) drawCalls++;
        if (this.atmosphereGlow && this.atmosphereGlow.visible) drawCalls++;
        if (this.atmosphericParticles && this.atmosphericParticles.visible) drawCalls++;
        if (this.cloudMesh && this.cloudMesh.visible) drawCalls++;

        this.performanceStats.drawCalls = drawCalls;
        document.getElementById('draw-calls').textContent = drawCalls;

        // Draw FPS graph
        this.drawFpsGraph();
      }

      drawFpsGraph() {
        if (!this.fpsCtx) return;

        const ctx = this.fpsCtx;
        const width = this.fpsCanvas.width;
        const height = this.fpsCanvas.height;

        // Clear canvas
        ctx.fillStyle = '#111827';
        ctx.fillRect(0, 0, width, height);

        // Draw FPS line
        ctx.strokeStyle = '#10b981';
        ctx.lineWidth = 1;
        ctx.beginPath();

        for (let i = 0; i < this.fpsHistory.length; i++) {
          const x = (i / (this.fpsHistory.length - 1)) * width;
          const y = height - (this.fpsHistory[i] / 120) * height; // Scale to 120 FPS max

          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }

        ctx.stroke();

        // Draw 60 FPS reference line
        ctx.strokeStyle = '#374151';
        ctx.lineWidth = 1;
        ctx.setLineDash([2, 2]);
        ctx.beginPath();
        const y60 = height - (60 / 120) * height;
        ctx.moveTo(0, y60);
        ctx.lineTo(width, y60);
        ctx.stroke();
        ctx.setLineDash([]);

        // Draw current FPS value
        ctx.fillStyle = '#10b981';
        ctx.font = '10px monospace';
        ctx.textAlign = 'right';
        const currentFps = this.fpsHistory[this.fpsHistory.length - 1];
        ctx.fillText(currentFps + ' FPS', width - 2, 12);
      }

      generateWeatherSystems() {
        // Generate dynamic weather systems
        this.weatherSystems = [];

        // Create 6-8 major weather systems
        const systemCount = 6 + Math.floor(EnhancedNoise.hash2D(this.seed, 100) * 3);

        for (let i = 0; i < systemCount; i++) {
          const theta = (i / systemCount) * Math.PI * 2 + EnhancedNoise.hash2D(this.seed, i) * Math.PI * 0.3;
          const phi = Math.PI * 0.3 + (EnhancedNoise.hash2D(this.seed + 50, i) - 0.5) * Math.PI * 0.4;

          const center = {
            x: Math.sin(phi) * Math.cos(theta),
            y: Math.cos(phi),
            z: Math.sin(phi) * Math.sin(theta)
          };

          const systemType = EnhancedNoise.hash2D(this.seed + 100, i) > 0.5 ? 'cyclone' : 'anticyclone';
          const intensity = 0.3 + EnhancedNoise.hash2D(this.seed + 150, i) * 1.2;
          const radius = 0.15 + EnhancedNoise.hash2D(this.seed + 200, i) * 0.25;
          const speed = 0.002 + EnhancedNoise.hash2D(this.seed + 250, i) * 0.008;

          this.weatherSystems.push({
            id: i,
            type: systemType,
            center,
            intensity,
            radius,
            speed,
            rotation: systemType === 'cyclone' ? -1 : 1,
            age: EnhancedNoise.hash2D(this.seed + 300, i) * 100,
            direction: {
              x: (EnhancedNoise.hash2D(this.seed + 350, i) - 0.5) * 0.02,
              y: (EnhancedNoise.hash2D(this.seed + 400, i) - 0.5) * 0.01,
              z: (EnhancedNoise.hash2D(this.seed + 450, i) - 0.5) * 0.02
            }
          });
        }
      }

      updateWeatherSystems(deltaTime) {
        // Update weather system positions and properties
        for (const system of this.weatherSystems) {
          // Move weather systems
          system.center.x += system.direction.x * deltaTime;
          system.center.y += system.direction.y * deltaTime;
          system.center.z += system.direction.z * deltaTime;

          // Normalize to keep on sphere surface
          const length = Math.sqrt(
            system.center.x * system.center.x +
            system.center.y * system.center.y +
            system.center.z * system.center.z
          );
          system.center.x /= length;
          system.center.y /= length;
          system.center.z /= length;

          // Update system properties
          system.age += deltaTime;

          // Seasonal intensity variation
          const seasonalEffect = Math.sin(this.seasonalCycle * Math.PI * 2) * 0.3 + 0.7;
          system.currentIntensity = system.intensity * seasonalEffect;

          // System evolution (birth, growth, decay)
          const lifespan = 50 + system.id * 10;
          const lifeCycle = (system.age % lifespan) / lifespan;

          if (lifeCycle < 0.2) {
            // Formation phase
            system.currentIntensity *= lifeCycle / 0.2;
          } else if (lifeCycle > 0.8) {
            // Decay phase
            system.currentIntensity *= (1 - lifeCycle) / 0.2;
          }
        }
      }

      getWeatherInfluence(position) {
        // Calculate weather influence at a given position
        let totalInfluence = 0;
        let dominantSystem = null;
        let maxInfluence = 0;

        for (const system of this.weatherSystems) {
          const distance = Math.sqrt(
            (position.x - system.center.x) * (position.x - system.center.x) +
            (position.y - system.center.y) * (position.y - system.center.y) +
            (position.z - system.center.z) * (position.z - system.center.z)
          );

          if (distance < system.radius) {
            const influence = (system.radius - distance) / system.radius * system.currentIntensity;
            totalInfluence += influence;

            if (influence > maxInfluence) {
              maxInfluence = influence;
              dominantSystem = system;
            }
          }
        }

        return {
          totalInfluence: Math.min(1, totalInfluence),
          dominantSystem,
          maxInfluence
        };
      }

      async generatePlanet() {
        if (this.isGenerating) return;

        this.isGenerating = true;
        this.generationCancelled = false;
        this.generationPaused = false;
        this.generationStartTime = performance.now();

        // Show progress overlay
        this.showProgress();

        try {
          // Step 1: Clear existing meshes and prepare
          this.updateProgress(
            'Preparing Generation',
            'Clearing existing planet data and initializing systems...',
            'geometry'
          );
          this.clearPlanetMeshes();
          await this.delay(100);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 2: Create base geometry
          this.updateProgress(
            'Generating Base Geometry',
            `Creating icosahedron with ${this.terrainDetail} subdivision levels...`,
            'geometry'
          );
          const geometry = this.createIcosahedronGeometry(1.0, this.terrainDetail);

          // Update vertex count
          document.getElementById('progress-vertices').textContent = geometry.attributes.position.count.toLocaleString();
          this.completeStep('geometry');
          await this.delay(150);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 3: Apply tectonic processes
          this.updateProgress(
            'Applying Tectonic Processes',
            'Generating plate boundaries, mountain ranges, and volcanic features...',
            'tectonics'
          );
          this.applyTectonicProcesses(geometry);
          this.completeStep('tectonics');
          await this.delay(200);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 4: Calculate climate effects
          this.updateProgress(
            'Calculating Climate Effects',
            'Determining temperature, precipitation, and biome distribution...',
            'climate'
          );
          this.applyClimateEffects(geometry);
          this.completeStep('climate');
          await this.delay(150);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 5: Apply erosion modeling
          this.updateProgress(
            'Applying Erosion Modeling',
            'Simulating weathering, river valleys, and landscape evolution...',
            'erosion'
          );
          this.applyErosion(geometry);
          this.completeStep('erosion');
          await this.delay(100);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 6: Apply advanced geological features
          this.updateProgress(
            'Applying Advanced Geology',
            'Adding age-dependent features: craters, volcanism, and geological evolution...',
            'atmosphere'
          );
          this.createAdvancedGeologicalFeatures();
          await this.delay(100);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 7: Create planet mesh with LOD system
          this.updateProgress(
            'Creating Planet Surface',
            'Generating materials and LOD system...',
            'atmosphere'
          );

          // Cache geometry for selective regeneration
          this.geometryCache = geometry.clone();

          // Create LOD system
          if (this.dynamicLOD) {
            await this.createLODSystem(geometry);
          } else {
            const material = this.createPlanetMaterial();
            this.planetMesh = new THREE.Mesh(geometry, material);
            this.planetMesh.castShadow = true;
            this.planetMesh.receiveShadow = true;
            this.scene.add(this.planetMesh);
          }
          await this.delay(100);

          // Step 7: Generate atmosphere
          if (this.features.atmosphere && this.atmPressure > 0.1) {
            this.updateProgress(
              'Generating Atmosphere',
              'Creating atmospheric layers, particles, and scattering effects...',
              'atmosphere'
            );
            this.createAtmosphere();
          }
          this.completeStep('atmosphere');
          await this.delay(100);

          // Step 8: Generate weather systems
          this.updateProgress(
            'Creating Weather Systems',
            'Generating cyclones, anticyclones, and atmospheric dynamics...',
            'weather'
          );
          this.generateWeatherSystems();
          this.completeStep('weather');
          await this.delay(100);

          // Step 9: Generate clouds
          if (this.features.clouds && this.cloudCoverage > 0.1) {
            this.updateProgress(
              'Generating Cloud Patterns',
              'Creating procedural cloud textures and weather-driven patterns...',
              'clouds'
            );
            this.createClouds();
          }
          this.completeStep('clouds');
          await this.delay(100);

          // Step 10: Create advanced features
          this.updateProgress(
            'Creating Advanced Features',
            'Generating aurora, planetary rings, and magnetic field effects...',
            'finalize'
          );

          if (this.features.aurora && this.magneticField > 0.3) {
            this.createAurora();
          }

          if (this.features.rings) {
            this.createPlanetaryRings();
          }

          await this.delay(100);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 11: Create environmental systems
          this.updateProgress(
            'Creating Environmental Systems',
            'Generating lightning, particles, and atmospheric effects...',
            'environment'
          );

          if (this.features.lightning) {
            this.createLightningSystem();
          }

          if (this.features.particles) {
            this.createEnvironmentalParticleSystem();
          }

          await this.delay(100);

          if (this.generationCancelled) throw new Error('Generation cancelled');

          // Step 12: Finalize
          this.updateProgress(
            'Finalizing Planet',
            'Updating statistics, optimizing performance, and completing generation...',
            'finalize'
          );
          this.updateStats();
          this.calculatePlanetAnalysis();
          this.calculateAtmosphericComposition();
          this.completeStep('finalize');
          await this.delay(200);

          // Success message
          this.updateProgress(
            'Planet Generation Complete!',
            `Successfully generated planet with ${geometry.attributes.position.count.toLocaleString()} vertices in ${((performance.now() - this.generationStartTime) / 1000).toFixed(1)}s`
          );
          await this.delay(1000);

        } catch (error) {
          console.error('Planet generation error:', error);

          if (this.generationCancelled) {
            this.updateProgress(
              'Generation Cancelled',
              'Planet generation was cancelled by user request.',
              null
            );
          } else {
            this.updateProgress(
              'Generation Error',
              `An error occurred during planet generation: ${error.message}. Please try again.`,
              null
            );
          }
          await this.delay(2000);
        } finally {
          this.hideProgress();
        }
      }

      clearPlanetMeshes() {
        // Clear main planet components
        [this.planetMesh, this.atmosphereMesh, this.atmosphereGlow, this.atmosphericParticles].forEach(mesh => {
          if (mesh) {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(mat => mat.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });

        // Clear atmospheric layers
        [this.stratosphereMesh, this.mesosphereMesh, this.volumetricMesh, this.scatteringMesh].forEach(mesh => {
          if (mesh) {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) mesh.material.dispose();
          }
        });

        // Clear cloud layers
        [this.lowCloudMesh, this.midCloudMesh, this.highCloudMesh, this.volumetricCloudMesh, this.cloudShadowMesh].forEach(mesh => {
          if (mesh) {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (mesh.material.map) mesh.material.map.dispose();
              mesh.material.dispose();
            }
          }
        });

        // Clear aurora rings
        if (this.auroraRings) {
          this.auroraRings.forEach(ring => {
            this.scene.remove(ring);
            if (ring.geometry) ring.geometry.dispose();
            if (ring.material) ring.material.dispose();
          });
          this.auroraRings = null;
        }

        // Clear planetary rings
        if (this.planetRings) {
          this.scene.remove(this.planetRings);
          if (this.planetRings.geometry) this.planetRings.geometry.dispose();
          if (this.planetRings.material) {
            if (this.planetRings.material.map) this.planetRings.material.map.dispose();
            this.planetRings.material.dispose();
          }
          this.planetRings = null;
        }

        // Clear magnetic field lines
        if (this.magneticFieldLines) {
          this.magneticFieldLines.forEach(line => {
            this.scene.remove(line);
            if (line.geometry) line.geometry.dispose();
            if (line.material) line.material.dispose();
          });
          this.magneticFieldLines = null;
        }

        // Clear LOD meshes
        this.clearLODMeshes();

        // Reset all mesh references
        this.planetMesh = null;
        this.atmosphereMesh = null;
        this.atmosphereGlow = null;
        this.atmosphericParticles = null;
        this.stratosphereMesh = null;
        this.mesosphereMesh = null;
        this.volumetricMesh = null;
        this.scatteringMesh = null;
        this.lowCloudMesh = null;
        this.midCloudMesh = null;
        this.highCloudMesh = null;
        this.volumetricCloudMesh = null;
        this.cloudShadowMesh = null;
      }

      createIcosahedronGeometry(radius, subdivisions) {
        // Create base icosahedron
        const t = (1.0 + Math.sqrt(5.0)) / 2.0;

        const vertices = [
          [-1,  t,  0], [ 1,  t,  0], [-1, -t,  0], [ 1, -t,  0],
          [ 0, -1,  t], [ 0,  1,  t], [ 0, -1, -t], [ 0,  1, -t],
          [ t,  0, -1], [ t,  0,  1], [-t,  0, -1], [-t,  0,  1]
        ];

        // Normalize vertices
        for (let i = 0; i < vertices.length; i++) {
          const v = vertices[i];
          const length = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
          vertices[i] = [v[0] / length, v[1] / length, v[2] / length];
        }

        // Define faces
        const faces = [
          [0, 11, 5], [0, 5, 1], [0, 1, 7], [0, 7, 10], [0, 10, 11],
          [1, 5, 9], [5, 11, 4], [11, 10, 2], [10, 7, 6], [7, 1, 8],
          [3, 9, 4], [3, 4, 2], [3, 2, 6], [3, 6, 8], [3, 8, 9],
          [4, 9, 5], [2, 4, 11], [6, 2, 10], [8, 6, 7], [9, 8, 1]
        ];

        // Subdivide
        let currentVertices = vertices;
        let currentFaces = faces;

        for (let level = 0; level < subdivisions; level++) {
          const result = this.subdivideMesh(currentVertices, currentFaces);
          currentVertices = result.vertices;
          currentFaces = result.faces;
        }

        // Scale to radius
        for (let i = 0; i < currentVertices.length; i++) {
          const v = currentVertices[i];
          currentVertices[i] = [v[0] * radius, v[1] * radius, v[2] * radius];
        }

        // Create Three.js geometry
        const geometry = new THREE.BufferGeometry();

        const positions = [];
        const indices = [];

        for (let i = 0; i < currentVertices.length; i++) {
          positions.push(...currentVertices[i]);
        }

        for (let i = 0; i < currentFaces.length; i++) {
          indices.push(...currentFaces[i]);
        }

        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setIndex(indices);
        geometry.computeVertexNormals();

        return geometry;
      }

      subdivideMesh(vertices, faces) {
        const newVertices = [...vertices];
        const newFaces = [];
        const midpointCache = new Map();

        const getMidpoint = (i1, i2) => {
          const key = i1 < i2 ? `${i1}-${i2}` : `${i2}-${i1}`;

          if (midpointCache.has(key)) {
            return midpointCache.get(key);
          }

          const v1 = vertices[i1];
          const v2 = vertices[i2];

          const mid = [
            (v1[0] + v2[0]) / 2,
            (v1[1] + v2[1]) / 2,
            (v1[2] + v2[2]) / 2
          ];

          const length = Math.sqrt(mid[0] * mid[0] + mid[1] * mid[1] + mid[2] * mid[2]);
          const normalizedMid = [mid[0] / length, mid[1] / length, mid[2] / length];

          const newIndex = newVertices.length;
          newVertices.push(normalizedMid);
          midpointCache.set(key, newIndex);

          return newIndex;
        };

        for (let i = 0; i < faces.length; i++) {
          const face = faces[i];
          const v1 = face[0];
          const v2 = face[1];
          const v3 = face[2];

          const a = getMidpoint(v1, v2);
          const b = getMidpoint(v2, v3);
          const c = getMidpoint(v3, v1);

          newFaces.push([v1, a, c]);
          newFaces.push([v2, b, a]);
          newFaces.push([v3, c, b]);
          newFaces.push([a, b, c]);
        }

        return { vertices: newVertices, faces: newFaces };
      }

      async applyTectonicProcesses(geometry) {
        const positions = geometry.attributes.position;
        const vertex = new THREE.Vector3();

        // Create realistic elevation distribution
        const elevationStats = {
          oceanFloor: -0.15,      // Deep ocean basins
          continentalShelf: -0.02, // Shallow coastal areas
          seaLevel: 0.0,          // Reference level
          lowlands: 0.03,         // Plains and valleys
          highlands: 0.08,        // Hills and plateaus
          mountains: 0.25,        // Mountain peaks
          extremePeaks: 0.35      // Highest peaks
        };

        const totalVertices = positions.count;
        let processedVertices = 0;

        // Process vertices in chunks to prevent freezing
        for (let startIndex = 0; startIndex < totalVertices; startIndex += this.asyncChunkSize) {
          // Check for cancellation or pause
          if (this.generationCancelled) {
            throw new Error('Generation cancelled');
          }

          while (this.generationPaused) {
            await this.delay(100);
            if (this.generationCancelled) {
              throw new Error('Generation cancelled');
            }
          }

          const endIndex = Math.min(startIndex + this.asyncChunkSize, totalVertices);

          // Process chunk
          for (let i = startIndex; i < endIndex; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();

            // Enhanced tectonic elevation with realistic height distribution
            const warpedPos = this.applyDomainWarping(normal.x, normal.y, normal.z);

            // 1. Continental/Oceanic crust distribution (primary structure)
            const continentalCrust = this.calculateContinentalCrust(warpedPos);

            // 2. Ocean basin depth (realistic bathymetry)
            const oceanDepth = this.calculateOceanDepth(warpedPos, continentalCrust);

            // 3. Continental elevation (realistic topography)
            const continentalElevation = this.calculateContinentalElevation(warpedPos, continentalCrust);

            // 4. Mountain ranges (plate boundary effects)
            const mountainElevation = this.calculateMountainElevation(warpedPos, continentalCrust);

            // 5. Volcanic features (hotspots and arcs)
            const volcanicElevation = this.calculateVolcanicElevation(warpedPos);

            // 6. Rift valleys and trenches
            const riftDepression = this.calculateRiftDepression(warpedPos);

            // Combine elevations with realistic weighting
            let finalElevation = 0;

            if (continentalCrust < 0.3) {
              // Oceanic regions
              finalElevation = oceanDepth;
              finalElevation += volcanicElevation * 0.3; // Seamounts
              finalElevation -= riftDepression * 0.5; // Ocean ridges and trenches
            } else {
              // Continental regions
              finalElevation = continentalElevation;
              finalElevation += mountainElevation;
              finalElevation += volcanicElevation * 0.7;
              finalElevation -= riftDepression * 0.3;
            }

            // Apply realistic elevation constraints
            finalElevation = this.constrainElevation(finalElevation, elevationStats);

            // Apply to vertex with proper scaling
            const newLength = 1.0 + finalElevation;
            vertex.setLength(newLength);

            positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
          }

          processedVertices = endIndex;

          // Update progress
          const progress = (processedVertices / totalVertices) * 100;
          document.getElementById('progress-vertices').textContent = processedVertices.toLocaleString();
          document.getElementById('progress-details').textContent =
            `Processing tectonic features: ${processedVertices.toLocaleString()}/${totalVertices.toLocaleString()} vertices`;

          // Yield control to prevent freezing
          await this.delay(this.asyncDelay);
        }

        positions.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      calculateContinentalCrust(pos) {
        // Large-scale continental/oceanic distribution
        const continentalNoise = EnhancedNoise.fractal(
          pos.x * 1.5, pos.y * 1.5, pos.z * 1.5, 4, 0.7, this.seed, 0.9
        );

        // Add continental clustering
        const clusterNoise = EnhancedNoise.worley(
          pos.x * 0.8, pos.y * 0.8, pos.z * 0.8, this.seed + 50, 0.8
        );

        return (continentalNoise * 0.7 + (1 - clusterNoise) * 0.3);
      }

      calculateOceanDepth(pos, continentalCrust) {
        if (continentalCrust > 0.3) return 0; // Not oceanic

        // Realistic ocean depth distribution
        const abyssalNoise = EnhancedNoise.fractal(
          pos.x * 3, pos.y * 3, pos.z * 3, 5, 0.6, this.seed + 100, 0.7
        );

        // Ocean ridges (shallower areas)
        const ridgeNoise = EnhancedNoise.ridged(
          pos.x * 8, pos.y * 8, pos.z * 8, 3, this.seed + 150, 0.6
        );

        // Deep ocean trenches
        const trenchNoise = EnhancedNoise.worley(
          pos.x * 12, pos.y * 12, pos.z * 12, this.seed + 200, 0.4
        );

        let depth = -0.08; // Base ocean depth
        depth += abyssalNoise * -0.07; // Abyssal plains variation
        depth += ridgeNoise * 0.04; // Ocean ridges (positive)
        depth += Math.max(0, 1 - trenchNoise) * -0.05; // Deep trenches

        // Continental shelf transition
        const shelfTransition = Math.max(0, continentalCrust - 0.1) / 0.2;
        depth = THREE.MathUtils.lerp(depth, -0.02, shelfTransition);

        return Math.max(-0.2, depth); // Limit maximum depth
      }

      calculateContinentalElevation(pos, continentalCrust) {
        if (continentalCrust < 0.3) return 0; // Not continental

        // Base continental elevation
        const baseElevation = EnhancedNoise.fractal(
          pos.x * 4, pos.y * 4, pos.z * 4, 6, 0.5, this.seed + 300, 0.8
        );

        // Plateau formation
        const plateauNoise = EnhancedNoise.billowy(
          pos.x * 2, pos.y * 2, pos.z * 2, 4, this.seed + 350, 0.7
        );

        // River valleys and erosion
        const valleyNoise = EnhancedNoise.ridged(
          pos.x * 12, pos.y * 12, pos.z * 12, 4, this.seed + 400, 0.5
        );

        let elevation = baseElevation * 0.05; // Base continental height
        elevation += Math.max(0, plateauNoise - 0.3) * 0.06; // Plateaus
        elevation -= valleyNoise * 0.02; // Valley incision

        // Coastal transition (lower near ocean)
        const coastalEffect = Math.min(1, (continentalCrust - 0.3) / 0.4);
        elevation *= coastalEffect;

        return Math.max(0, elevation);
      }

      calculateMountainElevation(pos, continentalCrust) {
        // Mountains primarily on continental crust
        const continentalFactor = Math.max(0, continentalCrust - 0.4);

        // Plate boundary mountain ranges
        const mountainRanges = EnhancedNoise.worley(
          pos.x * 3, pos.y * 3, pos.z * 3, this.seed + 500, 0.8
        );

        // Mountain detail and peaks
        const mountainDetail = EnhancedNoise.ridged(
          pos.x * 15, pos.y * 15, pos.z * 15, 5, this.seed + 550, 0.7
        );

        // Fold mountain systems
        const foldMountains = EnhancedNoise.fractal(
          pos.x * 6, pos.y * 6, pos.z * 6, 4, 0.6, this.seed + 600, 0.8
        );

        let mountainHeight = 0;

        // Major mountain ranges
        const rangeIntensity = Math.max(0, 1 - mountainRanges);
        if (rangeIntensity > 0.7) {
          mountainHeight += (rangeIntensity - 0.7) * 0.15; // Base mountain height
          mountainHeight += mountainDetail * 0.08; // Peak detail
          mountainHeight += foldMountains * 0.05; // Fold complexity
        }

        // Scale by mountain formation parameter and continental presence
        mountainHeight *= this.mountainFormation * continentalFactor;

        return Math.min(0.3, mountainHeight); // Limit maximum mountain height
      }

      calculateVolcanicElevation(pos) {
        // Volcanic hotspots
        const hotspotNoise = EnhancedNoise.worley(
          pos.x * 8, pos.y * 8, pos.z * 8, this.seed + 700, 0.6
        );

        // Volcanic chains (island arcs)
        const volcanicChains = EnhancedNoise.ridged(
          pos.x * 5, pos.y * 5, pos.z * 5, 3, this.seed + 750, 0.7
        );

        let volcanicHeight = 0;

        // Hotspot volcanoes
        const hotspotIntensity = Math.max(0, 1 - hotspotNoise);
        if (hotspotIntensity > 0.8) {
          volcanicHeight += (hotspotIntensity - 0.8) * 0.12;
        }

        // Volcanic chains
        volcanicHeight += volcanicChains * 0.04;

        return volcanicHeight * this.volcanicActivity;
      }

      calculateRiftDepression(pos) {
        // Continental rifts
        const riftNoise = EnhancedNoise.billowy(
          pos.x * 4, pos.y * 4, pos.z * 4, 3, this.seed + 800, 0.6
        );

        // Ocean spreading centers
        const spreadingNoise = EnhancedNoise.ridged(
          pos.x * 10, pos.y * 10, pos.z * 10, 3, this.seed + 850, 0.5
        );

        let depression = 0;

        // Rift valleys
        depression += Math.max(0, riftNoise - 0.6) * 0.03;

        // Spreading centers (actually create ridges, so negative depression)
        depression -= spreadingNoise * 0.02;

        return depression * this.tectonicActivity;
      }

      constrainElevation(elevation, stats) {
        // Apply realistic elevation distribution constraints

        if (elevation < stats.oceanFloor) {
          return stats.oceanFloor;
        } else if (elevation < stats.continentalShelf) {
          // Ocean to shelf transition
          const t = (elevation - stats.oceanFloor) / (stats.continentalShelf - stats.oceanFloor);
          return THREE.MathUtils.lerp(stats.oceanFloor, stats.continentalShelf, t);
        } else if (elevation < stats.seaLevel) {
          // Shelf to sea level
          const t = (elevation - stats.continentalShelf) / (stats.seaLevel - stats.continentalShelf);
          return THREE.MathUtils.lerp(stats.continentalShelf, stats.seaLevel, t);
        } else if (elevation < stats.lowlands) {
          // Sea level to lowlands
          const t = (elevation - stats.seaLevel) / (stats.lowlands - stats.seaLevel);
          return THREE.MathUtils.lerp(stats.seaLevel, stats.lowlands, t);
        } else if (elevation < stats.highlands) {
          // Lowlands to highlands
          const t = (elevation - stats.lowlands) / (stats.highlands - stats.lowlands);
          return THREE.MathUtils.lerp(stats.lowlands, stats.highlands, t);
        } else if (elevation < stats.mountains) {
          // highlands to mountains
          const t = (elevation - stats.highlands) / (stats.mountains - stats.highlands);
          return THREE.MathUtils.lerp(stats.highlands, stats.mountains, t);
        } else {
          // Mountain peaks
          return Math.min(stats.extremePeaks, elevation);
        }
      }

      applyDomainWarping(x, y, z) {
        // Apply domain warping to create more natural, less regular patterns
        const warpStrength = 0.1;

        // First level of warping
        const warpX1 = EnhancedNoise.perlin3D(x * 2, y * 2, z * 2, this.seed + 1000) * warpStrength;
        const warpY1 = EnhancedNoise.perlin3D(x * 2 + 100, y * 2 + 100, z * 2 + 100, this.seed + 1100) * warpStrength;
        const warpZ1 = EnhancedNoise.perlin3D(x * 2 + 200, y * 2 + 200, z * 2 + 200, this.seed + 1200) * warpStrength;

        // Apply first warp
        const warpedX1 = x + warpX1;
        const warpedY1 = y + warpY1;
        const warpedZ1 = z + warpZ1;

        // Second level of warping for more complexity
        const warpX2 = EnhancedNoise.perlin3D(warpedX1 * 4, warpedY1 * 4, warpedZ1 * 4, this.seed + 1300) * warpStrength * 0.5;
        const warpY2 = EnhancedNoise.perlin3D(warpedX1 * 4 + 300, warpedY1 * 4 + 300, warpedZ1 * 4 + 300, this.seed + 1400) * warpStrength * 0.5;
        const warpZ2 = EnhancedNoise.perlin3D(warpedX1 * 4 + 400, warpedY1 * 4 + 400, warpedZ1 * 4 + 400, this.seed + 1500) * warpStrength * 0.5;

        return {
          x: warpedX1 + warpX2,
          y: warpedY1 + warpY2,
          z: warpedZ1 + warpZ2
        };
      }

      async createLODSystem(baseGeometry) {
        this.updateProgress('Creating LOD System', 'Generating multiple detail levels...', 'lod');

        // Clear existing LOD meshes
        this.clearLODMeshes();

        // Create multiple LOD levels
        const lodCount = Math.min(4, this.lodMaxDetail - this.terrainDetail + 1);
        this.lodMeshes = [];
        this.lodLevels = [];

        for (let i = 0; i < lodCount; i++) {
          const detailLevel = this.terrainDetail + i;
          const distance = Math.pow(2, i) * 2; // Exponential distance scaling

          let geometry;
          if (i === 0) {
            // Use base geometry for closest LOD
            geometry = baseGeometry.clone();
          } else {
            // Create higher detail geometry for closer viewing
            geometry = this.createIcosahedronGeometry(1.0, detailLevel);

            // Apply all the same processes as base geometry
            this.applyTectonicProcesses(geometry);
            this.applyClimateEffects(geometry);
            this.createAdvancedGeologicalFeatures();
          }

          // Apply surface detail enhancement
          this.applySurfaceDetailEnhancement(geometry, detailLevel);

          const material = this.createPlanetMaterial();
          const mesh = new THREE.Mesh(geometry, material);
          mesh.castShadow = true;
          mesh.receiveShadow = true;
          mesh.visible = false; // Initially hidden

          this.lodMeshes.push(mesh);
          this.lodLevels.push({
            mesh: mesh,
            geometry: geometry,
            detailLevel: detailLevel,
            distance: distance,
            vertexCount: geometry.attributes.position.count
          });

          this.scene.add(mesh);

          // Small delay to prevent blocking
          if (i > 0) await this.delay(50);
        }

        // Set initial LOD
        this.updateLOD();

        console.log(`Created ${lodCount} LOD levels:`, this.lodLevels.map(l =>
          `Level ${l.detailLevel}: ${l.vertexCount.toLocaleString()} vertices`
        ));
      }

      applySurfaceDetailEnhancement(geometry, detailLevel) {
        if (this.surfaceDetail <= 1.0) return;

        const positions = geometry.attributes.position;
        const vertex = new THREE.Vector3();
        const enhancementStrength = (this.surfaceDetail - 1.0) * 0.02;

        for (let i = 0; i < positions.count; i++) {
          vertex.fromBufferAttribute(positions, i);
          const normal = vertex.clone().normalize();

          // Add fine-scale surface detail
          const microDetail = EnhancedNoise.fractal(
            normal.x * 50 * detailLevel,
            normal.y * 50 * detailLevel,
            normal.z * 50 * detailLevel,
            4, 0.4, this.seed + 2000, 0.3
          );

          // Add surface roughness
          const surfaceRoughness = EnhancedNoise.turbulence(
            normal.x * 100 * detailLevel,
            normal.y * 100 * detailLevel,
            normal.z * 100 * detailLevel,
            3, this.seed + 2100, 0.2
          );

          const enhancement = (microDetail + surfaceRoughness) * enhancementStrength;
          vertex.setLength(vertex.length() + enhancement);

          positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
        }

        positions.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      updateLOD() {
        if (!this.dynamicLOD || this.lodMeshes.length === 0) return;

        const cameraDistance = this.camera.position.length();
        let bestLOD = 0;

        // Find the best LOD level based on camera distance
        for (let i = 0; i < this.lodLevels.length; i++) {
          if (cameraDistance <= this.lodLevels[i].distance * this.lodDistance) {
            bestLOD = i;
            break;
          }
          bestLOD = i;
        }

        // Update mesh visibility
        for (let i = 0; i < this.lodMeshes.length; i++) {
          this.lodMeshes[i].visible = (i === bestLOD);
        }

        // Update current planet mesh reference
        this.planetMesh = this.lodMeshes[bestLOD];

        // Update stats
        if (this.lodLevels[bestLOD]) {
          const currentLOD = this.lodLevels[bestLOD];
          document.getElementById('vertex-count').textContent = currentLOD.vertexCount.toLocaleString();
          document.getElementById('triangle-count').textContent = Math.floor(currentLOD.vertexCount / 3).toLocaleString();
        }
      }

      clearLODMeshes() {
        if (this.lodMeshes) {
          this.lodMeshes.forEach(mesh => {
            if (mesh) {
              this.scene.remove(mesh);
              if (mesh.geometry) mesh.geometry.dispose();
              if (mesh.material) mesh.material.dispose();
            }
          });
        }
        this.lodMeshes = [];
        this.lodLevels = [];
      }

      createAdaptiveSubdivision(baseGeometry, targetVertexCount) {
        // Adaptive subdivision based on surface curvature and detail requirements
        let geometry = baseGeometry.clone();
        let currentVertexCount = geometry.attributes.position.count;

        while (currentVertexCount < targetVertexCount) {
          const subdivisionResult = this.subdivideMesh(
            this.geometryToVerticesAndFaces(geometry).vertices,
            this.geometryToVerticesAndFaces(geometry).faces
          );

          geometry.dispose();
          geometry = this.createGeometryFromVerticesAndFaces(
            subdivisionResult.vertices,
            subdivisionResult.faces
          );

          currentVertexCount = geometry.attributes.position.count;

          // Prevent infinite loops
          if (currentVertexCount > targetVertexCount * 1.5) break;
        }

        return geometry;
      }

      geometryToVerticesAndFaces(geometry) {
        const positions = geometry.attributes.position;
        const indices = geometry.index;

        const vertices = [];
        for (let i = 0; i < positions.count; i++) {
          vertices.push({
            x: positions.getX(i),
            y: positions.getY(i),
            z: positions.getZ(i)
          });
        }

        const faces = [];
        if (indices) {
          for (let i = 0; i < indices.count; i += 3) {
            faces.push([
              indices.getX(i),
              indices.getY(i),
              indices.getZ(i)
            ]);
          }
        }

        return { vertices, faces };
      }

      createGeometryFromVerticesAndFaces(vertices, faces) {
        const geometry = new THREE.BufferGeometry();

        const positions = new Float32Array(vertices.length * 3);
        for (let i = 0; i < vertices.length; i++) {
          positions[i * 3] = vertices[i].x;
          positions[i * 3 + 1] = vertices[i].y;
          positions[i * 3 + 2] = vertices[i].z;
        }

        const indices = new Uint32Array(faces.length * 3);
        for (let i = 0; i < faces.length; i++) {
          indices[i * 3] = faces[i][0];
          indices[i * 3 + 1] = faces[i][1];
          indices[i * 3 + 2] = faces[i][2];
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setIndex(new THREE.BufferAttribute(indices, 1));
        geometry.computeVertexNormals();

        return geometry;
      }

      async applyClimateEffects(geometry) {
        const positions = geometry.attributes.position;
        const colors = new Float32Array(positions.count * 3);
        const vertex = new THREE.Vector3();

        const totalVertices = positions.count;
        let processedVertices = 0;

        // Process vertices in chunks to prevent freezing
        for (let startIndex = 0; startIndex < totalVertices; startIndex += this.asyncChunkSize) {
          // Check for cancellation or pause
          if (this.generationCancelled) {
            throw new Error('Generation cancelled');
          }

          while (this.generationPaused) {
            await this.delay(100);
            if (this.generationCancelled) {
              throw new Error('Generation cancelled');
            }
          }

          const endIndex = Math.min(startIndex + this.asyncChunkSize, totalVertices);

          // Process chunk
          for (let i = startIndex; i < endIndex; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();
            const elevation = vertex.length() - 1.0;

            // Calculate climate
            const latitude = Math.asin(normal.y);
            const temperature = this.globalTemp * (1 - Math.abs(latitude) * 0.3) - elevation * 50;
            const precipitation = this.precipitation * (1 + Math.cos(latitude * 2) * 0.5);

            // Determine biome and color
            const biomeColor = this.getBiomeColor(elevation, temperature, precipitation, normal);

            colors[i * 3] = biomeColor.r;
            colors[i * 3 + 1] = biomeColor.g;
            colors[i * 3 + 2] = biomeColor.b;
          }

          processedVertices = endIndex;

          // Update progress
          const progress = (processedVertices / totalVertices) * 100;
          document.getElementById('progress-vertices').textContent = processedVertices.toLocaleString();
          document.getElementById('progress-details').textContent =
            `Applying climate effects: ${processedVertices.toLocaleString()}/${totalVertices.toLocaleString()} vertices`;

          // Yield control to prevent freezing
          await this.delay(this.asyncDelay);
        }

        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      }

      getBiomeColor(elevation, temperature, precipitation, normal) {
        const latitude = Math.abs(Math.asin(normal.y));

        // Enhanced ocean system with depth zones
        if (elevation < -0.01) {
          const depth = Math.abs(elevation);
          const oceanTemp = temperature + 10; // Ocean temperature moderation

          if (depth > 0.15) {
            // Abyssal depths
            return { r: 0.02, g: 0.05, b: 0.15 };
          } else if (depth > 0.08) {
            // Deep ocean
            return { r: 0.05, g: 0.15, b: 0.4 };
          } else if (depth > 0.03) {
            // Continental shelf
            return { r: 0.1, g: 0.25, b: 0.6 };
          } else {
            // Shallow coastal waters
            const clarity = Math.max(0, 1 - precipitation * 0.3); // Clearer in low-precipitation areas
            return {
              r: 0.15 + clarity * 0.1,
              g: 0.4 + clarity * 0.2,
              b: 0.7 + clarity * 0.2
            };
          }
        }

        // Enhanced ice system
        const iceThreshold = 250 + latitude * 20; // Variable ice line
        if (temperature < iceThreshold) {
          const iceIntensity = (iceThreshold - temperature) / 30;
          const snowBrightness = 0.85 + Math.min(0.15, iceIntensity * 0.3);
          return {
            r: snowBrightness,
            g: snowBrightness + 0.05,
            b: Math.min(1.0, snowBrightness + 0.1)
          };
        }

        // Enhanced biome classification with smooth transitions
        const tempNorm = (temperature - 250) / 80; // Normalize temperature 250-330K
        const precipNorm = precipitation / 2.0; // Normalize precipitation 0-2
        const elevNorm = Math.max(0, elevation) / 0.3; // Normalize elevation 0-0.3

        // Biome determination with fuzzy boundaries
        let biomeWeights = {
          desert: 0,
          grassland: 0,
          savanna: 0,
          temperateForest: 0,
          tropicalForest: 0,
          borealForest: 0,
          tundra: 0,
          alpine: 0,
          volcanic: 0
        };

        // Desert biomes
        if (tempNorm > 0.6 && precipNorm < 0.4) {
          biomeWeights.desert = (tempNorm - 0.6) * (0.4 - precipNorm) * 2.5;
        }

        // Grassland
        if (tempNorm > 0.4 && tempNorm < 0.8 && precipNorm > 0.2 && precipNorm < 0.8) {
          biomeWeights.grassland = (1 - Math.abs(tempNorm - 0.6)) * (1 - Math.abs(precipNorm - 0.5));
        }

        // Savanna
        if (tempNorm > 0.7 && precipNorm > 0.3 && precipNorm < 1.2) {
          biomeWeights.savanna = (tempNorm - 0.7) * Math.max(0, 1 - Math.abs(precipNorm - 0.75));
        }

        // Temperate forest
        if (tempNorm > 0.3 && tempNorm < 0.7 && precipNorm > 0.6) {
          biomeWeights.temperateForest = (1 - Math.abs(tempNorm - 0.5)) * Math.min(1, precipNorm - 0.6);
        }

        // Tropical forest
        if (tempNorm > 0.6 && precipNorm > 1.0) {
          biomeWeights.tropicalForest = (tempNorm - 0.6) * Math.min(1, precipNorm - 1.0);
        }

        // Boreal forest
        if (tempNorm > 0.1 && tempNorm < 0.5 && precipNorm > 0.4) {
          biomeWeights.borealForest = (1 - Math.abs(tempNorm - 0.3)) * Math.min(1, precipNorm - 0.4);
        }

        // Tundra
        if (tempNorm < 0.3) {
          biomeWeights.tundra = (0.3 - tempNorm) * 3;
        }

        // Alpine (elevation-dependent)
        if (elevNorm > 0.5) {
          biomeWeights.alpine = (elevNorm - 0.5) * 2;
        }

        // Volcanic (near volcanic features)
        const volcanicNoise = EnhancedNoise.distortedNoise(
          normal.x * 6, normal.y * 6, normal.z * 6, this.seed + 200, 0.6
        );
        if (volcanicNoise > 0.4) {
          biomeWeights.volcanic = (volcanicNoise - 0.4) * 1.5;
        }

        // Find dominant biome and blend
        let maxWeight = 0;
        let dominantBiome = 'grassland';

        for (const [biome, weight] of Object.entries(biomeWeights)) {
          if (weight > maxWeight) {
            maxWeight = weight;
            dominantBiome = biome;
          }
        }

        // Biome color palette
        const biomeColors = {
          desert: { r: 0.9, g: 0.7, b: 0.3 },
          grassland: { r: 0.4, g: 0.7, b: 0.2 },
          savanna: { r: 0.6, g: 0.6, b: 0.2 },
          temperateForest: { r: 0.2, g: 0.6, b: 0.2 },
          tropicalForest: { r: 0.1, g: 0.5, b: 0.1 },
          borealForest: { r: 0.15, g: 0.4, b: 0.15 },
          tundra: { r: 0.5, g: 0.5, b: 0.4 },
          alpine: { r: 0.6, g: 0.5, b: 0.4 },
          volcanic: { r: 0.4, g: 0.2, b: 0.1 }
        };

        // Blend colors for smooth transitions
        let finalColor = { r: 0, g: 0, b: 0 };
        let totalWeight = 0;

        for (const [biome, weight] of Object.entries(biomeWeights)) {
          if (weight > 0.1) {
            const color = biomeColors[biome];
            finalColor.r += color.r * weight;
            finalColor.g += color.g * weight;
            finalColor.b += color.b * weight;
            totalWeight += weight;
          }
        }

        if (totalWeight > 0) {
          finalColor.r /= totalWeight;
          finalColor.g /= totalWeight;
          finalColor.b /= totalWeight;
        } else {
          finalColor = biomeColors.grassland;
        }

        // Add subtle variation for realism
        const variation = EnhancedNoise.perlin3D(
          normal.x * 30, normal.y * 30, normal.z * 30, this.seed + 1700
        ) * 0.1;

        return {
          r: Math.max(0, Math.min(1, finalColor.r + variation)),
          g: Math.max(0, Math.min(1, finalColor.g + variation)),
          b: Math.max(0, Math.min(1, finalColor.b + variation))
        };
      }

      applyErosion(geometry) {
        const positions = geometry.attributes.position;
        const vertex = new THREE.Vector3();

        for (let i = 0; i < positions.count; i++) {
          vertex.fromBufferAttribute(positions, i);
          const normal = vertex.clone().normalize();
          const elevation = vertex.length() - 1.0;

          // Calculate climate factors for erosion
          const latitude = Math.asin(normal.y);
          const temperature = this.globalTemp * (1 - Math.abs(latitude) * 0.3) - elevation * 50;
          const precipitation = this.precipitation * (1 + Math.cos(latitude * 2) * 0.5);

          // Multi-scale erosion patterns
          const primaryErosion = EnhancedNoise.fractal(
            normal.x * 8, normal.y * 8, normal.z * 8, 5, 0.6, this.seed + 900, 0.5
          );

          const detailErosion = EnhancedNoise.turbulence(
            normal.x * 20, normal.y * 20, normal.z * 20, 4, this.seed + 1000, 0.3
          );

          // River valley erosion patterns
          const riverErosion = EnhancedNoise.ridged(
            normal.x * 15, normal.y * 15, normal.z * 15, 4, this.seed + 1100, 0.4
          );

          // Climate-dependent erosion rates
          let erosionRate = this.erosionRate;

          // Temperature effects (freeze-thaw cycles)
          if (temperature > 250 && temperature < 290) {
            erosionRate *= 1.5; // Freeze-thaw zone
          }

          // Precipitation effects
          erosionRate *= (0.5 + precipitation * 0.5);

          // Elevation effects (exposure)
          erosionRate *= (1 + Math.max(0, elevation) * 3);

          // Combine erosion patterns
          const combinedErosion = primaryErosion * 0.6 +
                                 detailErosion * 0.3 +
                                 riverErosion * 0.1;

          // Apply differential erosion
          const erosionAmount = Math.abs(combinedErosion) * erosionRate * 0.025;

          // Protect very low areas from over-erosion
          const protectionFactor = elevation < -0.05 ? 0.1 : 1.0;

          vertex.setLength(vertex.length() - erosionAmount * protectionFactor);
          positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
        }

        positions.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      createPlanetMaterial() {
        // Enhanced material with realistic lighting properties
        const material = new THREE.MeshStandardMaterial({
          vertexColors: true,
          metalness: 0.05,
          roughness: 0.9,
          wireframe: this.wireframeMode,

          // Enhanced lighting properties
          envMapIntensity: 0.3,

          // Improved normal mapping simulation
          flatShading: false,

          // Better shadow reception
          shadowSide: THREE.DoubleSide
        });

        // Add subtle emissive properties for volcanic regions
        material.emissive = new THREE.Color(0x000000);
        material.emissiveIntensity = 0.0;

        return material;
      }

      createAtmosphere() {
        if (this.atmPressure < 0.1) return;

        // Create multi-layer atmospheric system
        this.createAtmosphericLayers();
        this.createVolumetricAtmosphere();
        this.createAtmosphericScattering();
        this.createAtmosphericParticles();
      }

      createAtmosphericLayers() {
        // Main atmosphere layer
        const atmosphereGeometry = new THREE.SphereGeometry(1.08, 64, 64);
        const atmosphereMaterial = this.createAdvancedAtmosphereMaterial();

        this.atmosphereMesh = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        this.scene.add(this.atmosphereMesh);

        // Stratosphere layer
        const stratosphereGeometry = new THREE.SphereGeometry(1.12, 48, 48);
        const stratosphereMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.65, 0.6, 0.4),
          transparent: true,
          opacity: this.atmPressure * 0.1,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        this.stratosphereMesh = new THREE.Mesh(stratosphereGeometry, stratosphereMaterial);
        this.scene.add(this.stratosphereMesh);

        // Mesosphere layer
        const mesosphereGeometry = new THREE.SphereGeometry(1.16, 32, 32);
        const mesosphereMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.7, 0.5, 0.3),
          transparent: true,
          opacity: this.atmPressure * 0.05,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        this.mesosphereMesh = new THREE.Mesh(mesosphereGeometry, mesosphereMaterial);
        this.scene.add(this.mesosphereMesh);
      }

      createAdvancedAtmosphereMaterial() {
        // Create atmospheric shader material with advanced effects
        return new THREE.ShaderMaterial({
          vertexShader: `
            varying vec3 vNormal;
            varying vec3 vPosition;
            varying vec3 vWorldPosition;
            varying float vAtmosphereIntensity;

            void main() {
              vNormal = normalize(normalMatrix * normal);
              vPosition = position;
              vWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;

              // Calculate atmosphere intensity based on viewing angle
              vec3 viewDirection = normalize(cameraPosition - vWorldPosition);
              float fresnel = 1.0 - abs(dot(vNormal, viewDirection));
              vAtmosphereIntensity = pow(fresnel, 2.0);

              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            varying vec3 vNormal;
            varying vec3 vPosition;
            varying vec3 vWorldPosition;
            varying float vAtmosphereIntensity;

            uniform float pressure;
            uniform float time;
            uniform vec3 sunDirection;
            uniform vec3 atmosphereColor;
            uniform float scatteringStrength;

            void main() {
              vec3 normal = normalize(vNormal);

              // Calculate sun angle for atmospheric coloring
              float sunDot = dot(normal, sunDirection);
              float sunAngle = acos(clamp(sunDot, -1.0, 1.0));

              // Atmospheric color based on sun position
              vec3 dayColor = vec3(0.4, 0.7, 1.0);
              vec3 sunsetColor = vec3(1.0, 0.6, 0.2);
              vec3 nightColor = vec3(0.1, 0.2, 0.4);

              float dayFactor = clamp(sunDot + 0.2, 0.0, 1.0);
              float sunsetFactor = 1.0 - abs(sunDot) * 2.0;
              sunsetFactor = clamp(sunsetFactor, 0.0, 1.0);

              vec3 atmosphereColor = mix(nightColor, dayColor, dayFactor);
              atmosphereColor = mix(atmosphereColor, sunsetColor, sunsetFactor * 0.8);

              // Add atmospheric turbulence
              float turbulence = sin(vWorldPosition.x * 10.0 + time * 0.5) *
                                sin(vWorldPosition.y * 8.0 + time * 0.3) *
                                sin(vWorldPosition.z * 12.0 + time * 0.7) * 0.1 + 1.0;

              // Calculate final color and opacity
              float opacity = vAtmosphereIntensity * pressure * scatteringStrength * turbulence;

              gl_FragColor = vec4(atmosphereColor, opacity);
            }
          `,
          uniforms: {
            pressure: { value: this.atmPressure },
            time: { value: 0 },
            sunDirection: { value: new THREE.Vector3(0.6, 0.4, 0.3).normalize() },
            atmosphereColor: { value: new THREE.Color(0.4, 0.7, 1.0) },
            scatteringStrength: { value: 0.5 }
          },
          transparent: true,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });
      }

      createVolumetricAtmosphere() {
        // Create volumetric atmospheric effect using instanced geometry
        const volumetricGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        const volumetricMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.6, 0.8, 0.7),
          transparent: true,
          opacity: 0.1,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const volumetricCount = Math.floor(this.atmPressure * 500);
        this.volumetricMesh = new THREE.InstancedMesh(volumetricGeometry, volumetricMaterial, volumetricCount);

        // Position volumetric particles in atmospheric shell
        const matrix = new THREE.Matrix4();
        const position = new THREE.Vector3();
        const scale = new THREE.Vector3();

        for (let i = 0; i < volumetricCount; i++) {
          // Random position in atmospheric shell
          const radius = 1.05 + Math.random() * 0.15;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          position.set(
            radius * Math.sin(phi) * Math.cos(theta),
            radius * Math.cos(phi),
            radius * Math.sin(phi) * Math.sin(theta)
          );

          // Random scale
          const scaleValue = 0.5 + Math.random() * 1.5;
          scale.set(scaleValue, scaleValue, scaleValue);

          matrix.compose(position, new THREE.Quaternion(), scale);
          this.volumetricMesh.setMatrixAt(i, matrix);
        }

        this.volumetricMesh.instanceMatrix.needsUpdate = true;
        this.scene.add(this.volumetricMesh);
      }

      createAtmosphericScattering() {
        // Create Rayleigh scattering effect
        const scatteringGeometry = new THREE.SphereGeometry(1.2, 64, 64);
        const scatteringMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.65, 1.0, 0.8),
          transparent: true,
          opacity: this.atmPressure * 0.08,
          side: THREE.BackSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        this.scatteringMesh = new THREE.Mesh(scatteringGeometry, scatteringMaterial);
        this.scene.add(this.scatteringMesh);
      }

      createAtmosphericParticles() {
        if (this.atmPressure < 0.3) return;

        const particleCount = Math.floor(this.atmPressure * 200);
        const particleGeometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
          // Create particles in atmospheric shell
          const radius = 1.05 + Math.random() * 0.1;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
          positions[i * 3 + 1] = radius * Math.cos(phi);
          positions[i * 3 + 2] = radius * Math.sin(phi) * Math.sin(theta);

          // Atmospheric particle colors (blue to white)
          const intensity = 0.3 + Math.random() * 0.7;
          colors[i * 3] = intensity * 0.7;     // R
          colors[i * 3 + 1] = intensity * 0.8; // G
          colors[i * 3 + 2] = intensity;       // B

          sizes[i] = 1 + Math.random() * 2;
        }

        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const particleMaterial = new THREE.PointsMaterial({
          size: 0.5,
          transparent: true,
          opacity: this.atmPressure * 0.4,
          vertexColors: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false,
          sizeAttenuation: true
        });

        this.atmosphericParticles = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(this.atmosphericParticles);
      }

      createAurora() {
        if (!this.features.aurora || this.magneticField < 0.3) return;

        const auroraGeometry = new THREE.RingGeometry(1.15, 1.25, 32, 8, 0, Math.PI * 2);

        // Create aurora material with animated colors
        const auroraMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color().setHSL(0.3 + Math.random() * 0.4, 0.8, 0.6),
          transparent: true,
          opacity: this.magneticField * 0.4,
          side: THREE.DoubleSide,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        // Create multiple aurora rings at different latitudes
        this.auroraRings = [];

        // Northern aurora
        const northAurora = new THREE.Mesh(auroraGeometry, auroraMaterial.clone());
        northAurora.rotation.x = Math.PI * 0.3;
        northAurora.position.y = 0.8;
        this.auroraRings.push(northAurora);
        this.scene.add(northAurora);

        // Southern aurora
        const southAurora = new THREE.Mesh(auroraGeometry, auroraMaterial.clone());
        southAurora.rotation.x = -Math.PI * 0.3;
        southAurora.position.y = -0.8;
        southAurora.material.color.setHSL(0.7 + Math.random() * 0.2, 0.8, 0.6);
        this.auroraRings.push(southAurora);
        this.scene.add(southAurora);
      }

      createPlanetaryRings() {
        if (!this.features.rings) return;

        const ringGeometry = new THREE.RingGeometry(1.5, 2.5, 64, 8);

        // Create ring texture
        const ringTexture = this.generateRingTexture();

        const ringMaterial = new THREE.MeshBasicMaterial({
          map: ringTexture,
          transparent: true,
          opacity: 0.7,
          side: THREE.DoubleSide,
          depthWrite: false
        });

        this.planetRings = new THREE.Mesh(ringGeometry, ringMaterial);
        this.planetRings.rotation.x = Math.PI * 0.5 + (Math.random() - 0.5) * 0.3; // Slight tilt
        this.scene.add(this.planetRings);
      }

      generateRingTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');

        // Create ring pattern
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);

        // Add ring gaps and density variations
        for (let i = 0; i < 20; i++) {
          const pos = i / 20;
          const density = Math.random() * 0.8 + 0.2;
          const gap = Math.random() > 0.8 ? 0 : density;

          gradient.addColorStop(pos, `rgba(200, 180, 150, ${gap})`);
        }

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add ring detail
        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const noise = EnhancedNoise.perlin3D(x * 0.1, y * 0.5, this.seed * 0.01);
            const alpha = Math.max(0, noise * 0.5 + 0.5);

            if (Math.random() > 0.95) {
              ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.3})`;
              ctx.fillRect(x, y, 1, 1);
            }
          }
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        return texture;
      }

      createAdvancedGeologicalFeatures() {
        // This method will be called during planet generation
        // to add advanced geological features based on geological time

        if (this.geologicalTime > 70) {
          // Ancient planet features
          this.addAncientCratering();
          this.addErosionalFeatures();
        }

        if (this.geologicalTime < 30) {
          // Young planet features
          this.addActiveVolcanism();
          this.addFreshImpactCraters();
        }
      }

      addAncientCratering() {
        // Add impact crater effects to ancient planets
        // This would modify the geometry to add crater-like depressions
        if (!this.planetMesh) return;

        const geometry = this.planetMesh.geometry;
        const positions = geometry.attributes.position;
        const vertex = new THREE.Vector3();

        // Add 5-10 major ancient craters
        const craterCount = 5 + Math.floor(Math.random() * 6);

        for (let c = 0; c < craterCount; c++) {
          // Random crater location
          const craterCenter = new THREE.Vector3(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
          ).normalize();

          const craterRadius = 0.1 + Math.random() * 0.15;
          const craterDepth = 0.02 + Math.random() * 0.03;

          for (let i = 0; i < positions.count; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();

            const distance = normal.distanceTo(craterCenter);

            if (distance < craterRadius) {
              const craterEffect = Math.cos((distance / craterRadius) * Math.PI * 0.5);
              const depression = craterEffect * craterDepth;

              vertex.setLength(vertex.length() - depression);
              positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
            }
          }
        }

        positions.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      addActiveVolcanism() {
        // Add active volcanic features to young planets
        if (!this.planetMesh) return;

        const geometry = this.planetMesh.geometry;
        const positions = geometry.attributes.position;
        const colors = geometry.attributes.color;
        const vertex = new THREE.Vector3();

        // Add 3-5 active volcanic regions
        const volcanoCount = 3 + Math.floor(Math.random() * 3);

        for (let v = 0; v < volcanoCount; v++) {
          const volcanoCenter = new THREE.Vector3(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
          ).normalize();

          const volcanoRadius = 0.05 + Math.random() * 0.1;
          const volcanoHeight = 0.03 + Math.random() * 0.05;

          for (let i = 0; i < positions.count; i++) {
            vertex.fromBufferAttribute(positions, i);
            const normal = vertex.clone().normalize();

            const distance = normal.distanceTo(volcanoCenter);

            if (distance < volcanoRadius) {
              const volcanoEffect = Math.cos((distance / volcanoRadius) * Math.PI * 0.5);
              const elevation = volcanoEffect * volcanoHeight;

              vertex.setLength(vertex.length() + elevation);
              positions.setXYZ(i, vertex.x, vertex.y, vertex.z);

              // Add volcanic coloring (red/orange)
              const intensity = volcanoEffect * 0.8;
              colors.setXYZ(i,
                Math.min(1, colors.getX(i) + intensity),
                Math.max(0, colors.getY(i) - intensity * 0.5),
                Math.max(0, colors.getZ(i) - intensity * 0.8)
              );
            }
          }
        }

        positions.needsUpdate = true;
        colors.needsUpdate = true;
        geometry.computeVertexNormals();
      }

      createMagneticFieldVisualization() {
        if (!this.features.magneticField || this.magneticField < 0.1) return;

        // Create magnetic field lines
        const fieldLines = [];
        const lineCount = Math.floor(this.magneticField * 12);

        for (let i = 0; i < lineCount; i++) {
          const angle = (i / lineCount) * Math.PI * 2;
          const fieldLine = this.createMagneticFieldLine(angle);
          fieldLines.push(fieldLine);
          this.scene.add(fieldLine);
        }

        this.magneticFieldLines = fieldLines;
      }

      createMagneticFieldLine(angle) {
        const points = [];
        const radius = 1.2;
        const height = 2.0;

        // Create dipole field line
        for (let t = 0; t <= 1; t += 0.05) {
          const theta = Math.PI * t;
          const r = radius * Math.sin(theta) * Math.sin(theta);
          const y = (height / 2) * Math.cos(theta);

          const x = r * Math.cos(angle);
          const z = r * Math.sin(angle);

          points.push(new THREE.Vector3(x, y, z));
        }

        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({
          color: new THREE.Color().setHSL(0.7, 0.8, 0.6),
          transparent: true,
          opacity: this.magneticField * 0.3
        });

        return new THREE.Line(geometry, material);
      }

      calculatePlanetAnalysis() {
        // Calculate habitability score
        let habitability = 0;

        // Temperature factor (optimal around 288K)
        const tempFactor = 1 - Math.abs(this.globalTemp - 288) / 100;
        habitability += Math.max(0, tempFactor) * 0.3;

        // Atmospheric pressure factor
        const pressureFactor = Math.min(1, this.atmPressure / 1.5);
        habitability += pressureFactor * 0.2;

        // Magnetic field protection
        const magneticFactor = Math.min(1, this.magneticField);
        habitability += magneticFactor * 0.2;

        // Water presence (ocean coverage)
        const waterFactor = Math.min(1, this.oceanCoverage * 1.5);
        habitability += waterFactor * 0.2;

        // Geological stability (moderate activity is good)
        const geoFactor = 1 - Math.abs(this.tectonicActivity - 1) / 2;
        habitability += Math.max(0, geoFactor) * 0.1;

        habitability = Math.max(0, Math.min(1, habitability));

        // Update analysis display
        document.getElementById('planet-age').textContent = this.geologicalTime + ' Myr';
        document.getElementById('habitability-score').textContent = habitability.toFixed(2);
        document.getElementById('habitability-bar').style.width = (habitability * 100) + '%';

        // Geological activity level
        let geoActivity = 'Low';
        if (this.tectonicActivity > 1.5) geoActivity = 'High';
        else if (this.tectonicActivity > 0.7) geoActivity = 'Moderate';
        document.getElementById('geological-activity').textContent = geoActivity;

        // Atmospheric density
        document.getElementById('atmospheric-density').textContent = this.atmPressure.toFixed(1) + ' atm';

        // Surface gravity (rough estimate based on size and density)
        const gravity = 9.8 * (1 + (this.tectonicActivity - 1) * 0.2);
        document.getElementById('surface-gravity').textContent = gravity.toFixed(1) + ' m/s²';

        // Magnetic protection
        let magneticProtection = 'None';
        if (this.magneticField > 1.5) magneticProtection = 'Very Strong';
        else if (this.magneticField > 1.0) magneticProtection = 'Strong';
        else if (this.magneticField > 0.5) magneticProtection = 'Moderate';
        else if (this.magneticField > 0.1) magneticProtection = 'Weak';
        document.getElementById('magnetic-protection').textContent = magneticProtection;

        return habitability;
      }

      calculateAtmosphericComposition() {
        // Calculate realistic atmospheric composition based on planet parameters
        let composition = { ...this.atmosphericComposition };

        // Temperature effects on atmospheric composition
        const tempFactor = this.globalTemp / 288; // Earth temperature baseline

        // Pressure effects
        const pressureFactor = this.atmPressure;

        // Geological activity effects
        const volcanicFactor = this.volcanicActivity;

        // Magnetic field effects (atmospheric retention)
        const magneticFactor = this.magneticField;

        // Calculate oxygen levels (affected by life, temperature, pressure)
        composition.oxygen = Math.max(0, Math.min(30,
          21 * pressureFactor * magneticFactor * (2 - tempFactor)
        ));

        // Calculate CO2 levels (affected by temperature, volcanic activity)
        composition.co2 = Math.max(0.01, Math.min(10,
          0.04 + volcanicFactor * 2 + Math.max(0, tempFactor - 1) * 5
        ));

        // Calculate water vapor (affected by temperature, pressure)
        composition.water = Math.max(0, Math.min(5,
          1 * pressureFactor * Math.min(2, tempFactor)
        ));

        // Calculate nitrogen (fills remaining space, affected by pressure retention)
        const otherGases = composition.oxygen + composition.co2 + composition.water;
        composition.nitrogen = Math.max(50, 100 - otherGases - 1);

        // Calculate trace gases
        composition.trace = Math.max(0, 100 - composition.nitrogen - composition.oxygen -
                                   composition.co2 - composition.water);

        // Normalize to 100%
        const total = composition.nitrogen + composition.oxygen + composition.co2 +
                     composition.water + composition.trace;

        Object.keys(composition).forEach(key => {
          composition[key] = (composition[key] / total) * 100;
        });

        this.atmosphericComposition = composition;
        this.updateAtmosphericCompositionUI();

        return composition;
      }

      updateAtmosphericCompositionUI() {
        const comp = this.atmosphericComposition;

        // Update percentages
        document.getElementById('nitrogen-percent').textContent = comp.nitrogen.toFixed(1) + '%';
        document.getElementById('oxygen-percent').textContent = comp.oxygen.toFixed(1) + '%';
        document.getElementById('co2-percent').textContent = comp.co2.toFixed(2) + '%';
        document.getElementById('water-percent').textContent = comp.water.toFixed(1) + '%';
        document.getElementById('trace-percent').textContent = comp.trace.toFixed(2) + '%';

        // Update progress bars
        document.getElementById('nitrogen-bar').style.width = comp.nitrogen + '%';
        document.getElementById('oxygen-bar').style.width = comp.oxygen + '%';
        document.getElementById('co2-bar').style.width = Math.max(1, comp.co2) + '%';
        document.getElementById('water-bar').style.width = comp.water + '%';
        document.getElementById('trace-bar').style.width = Math.max(1, comp.trace) + '%';

        // Calculate breathability
        let breathability = 'Toxic';
        if (comp.oxygen > 16 && comp.oxygen < 25 && comp.co2 < 1) {
          breathability = 'Excellent';
        } else if (comp.oxygen > 12 && comp.oxygen < 30 && comp.co2 < 2) {
          breathability = 'Good';
        } else if (comp.oxygen > 8 && comp.oxygen < 35 && comp.co2 < 5) {
          breathability = 'Poor';
        }
        document.getElementById('breathability').textContent = breathability;

        // Calculate greenhouse effect
        let greenhouse = 'Minimal';
        if (comp.co2 > 5) {
          greenhouse = 'Extreme';
        } else if (comp.co2 > 2) {
          greenhouse = 'Strong';
        } else if (comp.co2 > 0.5) {
          greenhouse = 'Moderate';
        }
        document.getElementById('greenhouse-effect').textContent = greenhouse;
      }

      updateMagneticField() {
        // Remove existing magnetic field lines
        if (this.magneticFieldLines) {
          this.magneticFieldLines.forEach(line => {
            this.scene.remove(line);
            if (line.geometry) line.geometry.dispose();
            if (line.material) line.material.dispose();
          });
          this.magneticFieldLines = null;
        }

        // Create new magnetic field visualization if enabled
        if (this.features.magneticField && this.magneticField > 0.1) {
          this.createMagneticFieldVisualization();
        }
      }

      createLightningSystem() {
        if (!this.features.lightning || this.lightningActivity < 0.1) return;

        // Lightning strikes are created dynamically during animation
        this.lightningStrikes = [];
        this.lastLightningTime = 0;
      }

      createLightningStrike() {
        if (!this.features.lightning) return;

        // Random position on planet surface
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos(2 * Math.random() - 1);

        const startPos = new THREE.Vector3(
          1.02 * Math.sin(phi) * Math.cos(theta),
          1.02 * Math.cos(phi),
          1.02 * Math.sin(phi) * Math.sin(theta)
        );

        // Lightning extends upward into atmosphere
        const endPos = startPos.clone().multiplyScalar(1.1 + Math.random() * 0.1);

        // Create lightning geometry
        const lightningGeometry = new THREE.BufferGeometry();
        const positions = [];
        const colors = [];

        // Create jagged lightning path
        const segments = 8 + Math.floor(Math.random() * 8);
        for (let i = 0; i <= segments; i++) {
          const t = i / segments;
          const pos = startPos.clone().lerp(endPos, t);

          // Add random jaggedness
          if (i > 0 && i < segments) {
            const jag = new THREE.Vector3(
              (Math.random() - 0.5) * 0.05,
              (Math.random() - 0.5) * 0.05,
              (Math.random() - 0.5) * 0.05
            );
            pos.add(jag);
          }

          positions.push(pos.x, pos.y, pos.z);

          // Lightning color (bright white to blue)
          const intensity = 1 - t * 0.3;
          colors.push(intensity, intensity, 1);
        }

        lightningGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        lightningGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

        const lightningMaterial = new THREE.LineBasicMaterial({
          vertexColors: true,
          transparent: true,
          opacity: 0.9,
          linewidth: 2
        });

        const lightningMesh = new THREE.Line(lightningGeometry, lightningMaterial);

        // Lightning strike properties
        const strike = {
          mesh: lightningMesh,
          life: 0.2 + Math.random() * 0.3, // Duration in seconds
          maxLife: 0.2 + Math.random() * 0.3,
          intensity: 0.8 + Math.random() * 0.2
        };

        this.lightningStrikes.push(strike);
        this.scene.add(lightningMesh);

        // Create lightning flash effect
        this.createLightningFlash(startPos);
      }

      createLightningFlash(position) {
        // Create brief flash effect
        const flashGeometry = new THREE.SphereGeometry(0.05, 8, 8);
        const flashMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(1, 1, 0.8),
          transparent: true,
          opacity: 0.8,
          blending: THREE.AdditiveBlending
        });

        const flashMesh = new THREE.Mesh(flashGeometry, flashMaterial);
        flashMesh.position.copy(position);

        const flash = {
          mesh: flashMesh,
          life: 0.1,
          maxLife: 0.1
        };

        this.lightningStrikes.push(flash);
        this.scene.add(flashMesh);
      }

      updateLightningSystem(deltaTime) {
        if (!this.features.lightning) return;

        // Generate new lightning strikes
        const lightningInterval = Math.max(0.5, 3 - this.lightningActivity * 2);
        if (performance.now() - this.lastLightningTime > lightningInterval * 1000) {
          // Higher chance during storms
          const stormBonus = this.weatherSystems.filter(s => s.type === 'cyclone').length * 0.3;
          const lightningChance = (this.lightningActivity + stormBonus) * 0.3;

          if (Math.random() < lightningChance) {
            this.createLightningStrike();
            this.lastLightningTime = performance.now();
          }
        }

        // Update existing lightning strikes
        for (let i = this.lightningStrikes.length - 1; i >= 0; i--) {
          const strike = this.lightningStrikes[i];
          strike.life -= deltaTime;

          if (strike.life <= 0) {
            // Remove expired lightning
            this.scene.remove(strike.mesh);
            if (strike.mesh.geometry) strike.mesh.geometry.dispose();
            if (strike.mesh.material) strike.mesh.material.dispose();
            this.lightningStrikes.splice(i, 1);
          } else {
            // Fade out lightning
            const fadeRatio = strike.life / strike.maxLife;
            strike.mesh.material.opacity = fadeRatio * strike.intensity;
          }
        }
      }

      createEnvironmentalParticleSystem() {
        if (!this.features.particles || this.particleDensity < 0.1) return;

        const particleCount = Math.floor(this.particleDensity * 2000);
        const particleGeometry = new THREE.BufferGeometry();

        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
          // Random position in atmospheric shell
          const radius = 1.05 + Math.random() * 0.2;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
          positions[i * 3 + 1] = radius * Math.cos(phi);
          positions[i * 3 + 2] = radius * Math.sin(phi) * Math.sin(theta);

          // Random velocity
          velocities[i * 3] = (Math.random() - 0.5) * 0.01;
          velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.01;
          velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.01;

          // Particle color (dust, pollen, etc.)
          const particleType = Math.random();
          if (particleType < 0.6) {
            // Dust particles (brown/tan)
            colors[i * 3] = 0.8 + Math.random() * 0.2;
            colors[i * 3 + 1] = 0.6 + Math.random() * 0.2;
            colors[i * 3 + 2] = 0.3 + Math.random() * 0.2;
          } else if (particleType < 0.8) {
            // Water droplets (blue/white)
            colors[i * 3] = 0.7 + Math.random() * 0.3;
            colors[i * 3 + 1] = 0.8 + Math.random() * 0.2;
            colors[i * 3 + 2] = 1.0;
          } else {
            // Organic particles (green/yellow)
            colors[i * 3] = 0.4 + Math.random() * 0.4;
            colors[i * 3 + 1] = 0.7 + Math.random() * 0.3;
            colors[i * 3 + 2] = 0.2 + Math.random() * 0.3;
          }

          sizes[i] = 0.5 + Math.random() * 2;
        }

        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const particleMaterial = new THREE.PointsMaterial({
          size: 1,
          transparent: true,
          opacity: this.particleDensity * 0.3,
          vertexColors: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false,
          sizeAttenuation: true
        });

        this.environmentalParticles = new THREE.Points(particleGeometry, particleMaterial);
        this.scene.add(this.environmentalParticles);
      }

      updateEnvironmentalParticles(deltaTime) {
        if (!this.environmentalParticles) return;

        const positions = this.environmentalParticles.geometry.attributes.position;
        const velocities = this.environmentalParticles.geometry.attributes.velocity;

        for (let i = 0; i < positions.count; i++) {
          // Update position based on velocity
          positions.array[i * 3] += velocities.array[i * 3] * deltaTime * 60;
          positions.array[i * 3 + 1] += velocities.array[i * 3 + 1] * deltaTime * 60;
          positions.array[i * 3 + 2] += velocities.array[i * 3 + 2] * deltaTime * 60;

          // Add atmospheric circulation effects
          const pos = new THREE.Vector3(
            positions.array[i * 3],
            positions.array[i * 3 + 1],
            positions.array[i * 3 + 2]
          );

          const distance = pos.length();

          // Reset particles that drift too far
          if (distance > 1.3 || distance < 1.0) {
            const radius = 1.05 + Math.random() * 0.2;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);

            positions.array[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions.array[i * 3 + 1] = radius * Math.cos(phi);
            positions.array[i * 3 + 2] = radius * Math.sin(phi) * Math.sin(theta);
          }
        }

        positions.needsUpdate = true;
      }

      updateVolumetricFeatures() {
        // Update volumetric atmosphere
        if (this.volumetricMesh) {
          this.volumetricMesh.visible = this.features.volumetric;
        }

        // Update volumetric clouds
        if (this.volumetricCloudMesh) {
          this.volumetricCloudMesh.visible = this.features.volumetric && this.features.clouds;
        }
      }

      updateShadowFeatures() {
        // Update cloud shadows
        if (this.cloudShadowMesh) {
          this.cloudShadowMesh.visible = this.features.shadows && this.features.clouds;
        }

        // Update renderer shadow settings
        this.renderer.shadowMap.enabled = this.features.shadows;
      }

      createClouds() {
        // Create multi-layer cloud system
        this.createLowClouds();
        this.createMidClouds();
        this.createHighClouds();
        this.createVolumetricClouds();
        this.createCloudShadows();
      }

      createLowClouds() {
        // Low altitude clouds (cumulus, stratus)
        const lowCloudGeometry = new THREE.SphereGeometry(1.02, 64, 64);
        const lowCloudTexture = this.generateAdvancedCloudTexture('low');

        const lowCloudMaterial = new THREE.MeshLambertMaterial({
          map: lowCloudTexture,
          transparent: true,
          opacity: this.cloudCoverage * 0.8,
          depthWrite: false,
          alphaTest: 0.1
        });

        this.lowCloudMesh = new THREE.Mesh(lowCloudGeometry, lowCloudMaterial);
        this.scene.add(this.lowCloudMesh);
      }

      createMidClouds() {
        // Mid altitude clouds (altocumulus, altostratus)
        const midCloudGeometry = new THREE.SphereGeometry(1.04, 48, 48);
        const midCloudTexture = this.generateAdvancedCloudTexture('mid');

        const midCloudMaterial = new THREE.MeshBasicMaterial({
          map: midCloudTexture,
          transparent: true,
          opacity: this.cloudCoverage * 0.5,
          depthWrite: false,
          blending: THREE.AdditiveBlending
        });

        this.midCloudMesh = new THREE.Mesh(midCloudGeometry, midCloudMaterial);
        this.scene.add(this.midCloudMesh);
      }

      createHighClouds() {
        // High altitude clouds (cirrus, cirrostratus)
        const highCloudGeometry = new THREE.SphereGeometry(1.06, 32, 32);
        const highCloudTexture = this.generateAdvancedCloudTexture('high');

        const highCloudMaterial = new THREE.MeshBasicMaterial({
          map: highCloudTexture,
          transparent: true,
          opacity: this.cloudCoverage * 0.3,
          depthWrite: false,
          blending: THREE.AdditiveBlending
        });

        this.highCloudMesh = new THREE.Mesh(highCloudGeometry, highCloudMaterial);
        this.scene.add(this.highCloudMesh);
      }

      createVolumetricClouds() {
        // Create 3D volumetric cloud particles
        const cloudParticleGeometry = new THREE.SphereGeometry(0.01, 6, 6);
        const cloudParticleMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(1, 1, 1),
          transparent: true,
          opacity: 0.15,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const particleCount = Math.floor(this.cloudCoverage * 1000);
        this.volumetricCloudMesh = new THREE.InstancedMesh(
          cloudParticleGeometry,
          cloudParticleMaterial,
          particleCount
        );

        // Position cloud particles based on weather systems
        this.updateVolumetricCloudPositions();
        this.scene.add(this.volumetricCloudMesh);
      }

      updateVolumetricCloudPositions() {
        if (!this.volumetricCloudMesh) return;

        const matrix = new THREE.Matrix4();
        const position = new THREE.Vector3();
        const scale = new THREE.Vector3();
        const quaternion = new THREE.Quaternion();

        for (let i = 0; i < this.volumetricCloudMesh.count; i++) {
          // Random base position
          const radius = 1.015 + Math.random() * 0.02;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          position.set(
            radius * Math.sin(phi) * Math.cos(theta),
            radius * Math.cos(phi),
            radius * Math.sin(phi) * Math.sin(theta)
          );

          // Enhance density near weather systems
          const weatherInfluence = this.getWeatherInfluence(position.clone().normalize());
          let density = 1.0;

          if (weatherInfluence.dominantSystem) {
            if (weatherInfluence.dominantSystem.type === 'cyclone') {
              density += weatherInfluence.maxInfluence * 3;
            } else {
              density *= (1 - weatherInfluence.maxInfluence * 0.7);
            }
          }

          // Skip particle if density is too low
          if (density < 0.3) {
            scale.set(0, 0, 0);
          } else {
            const scaleValue = (0.5 + Math.random() * 1.5) * density;
            scale.set(scaleValue, scaleValue, scaleValue);
          }

          matrix.compose(position, quaternion, scale);
          this.volumetricCloudMesh.setMatrixAt(i, matrix);
        }

        this.volumetricCloudMesh.instanceMatrix.needsUpdate = true;
      }

      createCloudShadows() {
        // Create cloud shadow projection on planet surface
        const shadowGeometry = new THREE.SphereGeometry(1.001, 64, 64);
        const shadowTexture = this.generateCloudShadowTexture();

        const shadowMaterial = new THREE.MeshBasicMaterial({
          map: shadowTexture,
          transparent: true,
          opacity: this.cloudCoverage * 0.3,
          blending: THREE.MultiplyBlending,
          depthWrite: false
        });

        this.cloudShadowMesh = new THREE.Mesh(shadowGeometry, shadowMaterial);
        this.scene.add(this.cloudShadowMesh);
      }

      generateAdvancedCloudTexture(cloudType = 'low') {
        const canvas = document.createElement('canvas');
        canvas.width = 2048;
        canvas.height = 1024;
        const ctx = canvas.getContext('2d');

        const imageData = ctx.createImageData(canvas.width, canvas.height);
        const data = imageData.data;

        // Cloud type parameters
        const cloudParams = this.getCloudTypeParameters(cloudType);

        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const u = x / canvas.width;
            const v = y / canvas.height;

            // Convert to spherical coordinates
            const theta = u * Math.PI * 2;
            const phi = v * Math.PI;

            const nx = Math.sin(phi) * Math.cos(theta);
            const ny = Math.cos(phi);
            const nz = Math.sin(phi) * Math.sin(theta);

            let cloudDensity = this.calculateAdvancedCloudDensity(nx, ny, nz, cloudParams);

            // Apply weather system effects
            const position = { x: nx, y: ny, z: nz };
            cloudDensity = this.applyWeatherSystemEffects(cloudDensity, position, cloudParams);

            // Apply atmospheric dynamics
            cloudDensity = this.applyAtmosphericDynamics(cloudDensity, nx, ny, nz, cloudParams);

            const index = (y * canvas.width + x) * 4;
            const alpha = Math.min(255, Math.max(0, cloudDensity * 255));

            // Cloud color based on type and lighting
            const cloudColor = this.calculateCloudColor(nx, ny, nz, cloudDensity, cloudParams);

            data[index] = cloudColor.r;     // R
            data[index + 1] = cloudColor.g; // G
            data[index + 2] = cloudColor.b; // B
            data[index + 3] = alpha;        // A
          }
        }

        ctx.putImageData(imageData, 0, 0);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        texture.generateMipmaps = true;
        texture.minFilter = THREE.LinearMipmapLinearFilter;
        texture.magFilter = THREE.LinearFilter;

        return texture;
      }

      getCloudTypeParameters(cloudType) {
        const params = {
          low: {
            baseScale: 2,
            detailScale: 8,
            wispScale: 20,
            density: 1.0,
            coverage: 0.7,
            thickness: 0.8,
            brightness: 0.9,
            contrast: 1.2
          },
          mid: {
            baseScale: 3,
            detailScale: 12,
            wispScale: 30,
            density: 0.7,
            coverage: 0.5,
            thickness: 0.6,
            brightness: 0.8,
            contrast: 1.0
          },
          high: {
            baseScale: 5,
            detailScale: 20,
            wispScale: 50,
            density: 0.4,
            coverage: 0.3,
            thickness: 0.3,
            brightness: 0.95,
            contrast: 0.8
          }
        };

        return params[cloudType] || params.low;
      }

      calculateAdvancedCloudDensity(nx, ny, nz, params) {
        // Multi-octave cloud base
        const cloudBase = EnhancedNoise.fractal(
          nx * params.baseScale, ny * params.baseScale, nz * params.baseScale,
          6, 0.6, this.seed + 1200, 0.8
        );

        // Cloud detail layer
        const cloudDetail = EnhancedNoise.billowy(
          nx * params.detailScale, ny * params.detailScale, nz * params.detailScale,
          4, this.seed + 1300, 0.6
        );

        // Fine cloud wisps
        const cloudWisps = EnhancedNoise.turbulence(
          nx * params.wispScale, ny * params.wispScale, nz * params.wispScale,
          3, this.seed + 1400, 0.4
        );

        // Cloud anvils (for cumulonimbus)
        const cloudAnvils = EnhancedNoise.ridged(
          nx * params.baseScale * 0.5, ny * params.baseScale * 0.5, nz * params.baseScale * 0.5,
          3, this.seed + 1500, 0.7
        );

        // Combine layers with proper weighting
        let cloudDensity = cloudBase * 0.5 +
                          cloudDetail * 0.3 +
                          cloudWisps * 0.15 +
                          cloudAnvils * 0.05;

        // Apply latitude effects
        const latitude = Math.abs(ny);
        const latitudeEffect = this.calculateLatitudeCloudEffect(latitude);
        cloudDensity *= latitudeEffect;

        // Apply coverage and density parameters
        cloudDensity = Math.max(0, cloudDensity - (1 - params.coverage)) * params.density;

        return cloudDensity;
      }

      calculateLatitudeCloudEffect(latitude) {
        // More realistic latitude-based cloud distribution
        // High clouds near equator (ITCZ), mid-latitude storm tracks, polar regions

        const equatorialClouds = Math.exp(-Math.pow(latitude * 4, 2)); // Strong equatorial band
        const midLatitudeClouds = Math.exp(-Math.pow((latitude - 0.6) * 8, 2)); // Mid-latitude storm tracks
        const polarClouds = Math.exp(-Math.pow((latitude - 1.2) * 6, 2)) * 0.7; // Polar clouds

        return Math.max(0.3, equatorialClouds + midLatitudeClouds + polarClouds);
      }

      applyWeatherSystemEffects(cloudDensity, position, params) {
        const weatherInfluence = this.getWeatherInfluence(position);

        if (weatherInfluence.dominantSystem) {
          const system = weatherInfluence.dominantSystem;

          if (system.type === 'cyclone') {
            // Cyclones enhance cloud formation
            const enhancement = weatherInfluence.maxInfluence * 2.0;
            cloudDensity += enhancement;

            // Add spiral structure
            const dx = position.x - system.center.x;
            const dz = position.z - system.center.z;
            const angle = Math.atan2(dz, dx);
            const distance = Math.sqrt(dx * dx + dz * dz);

            const spiralPhase = angle * 3 + distance * 10 + this.weatherTime * system.rotation * 3;
            const spiralEffect = Math.sin(spiralPhase) * 0.5 + 0.5;
            cloudDensity *= (0.7 + spiralEffect * 0.6);

          } else {
            // Anticyclones suppress clouds
            const suppression = weatherInfluence.maxInfluence * 0.8;
            cloudDensity *= (1 - suppression);
          }
        }

        return cloudDensity;
      }

      applyAtmosphericDynamics(cloudDensity, nx, ny, nz, params) {
        // Seasonal effects
        const latitude = Math.abs(ny);
        const seasonalEffect = Math.sin(this.seasonalCycle * Math.PI * 2 + latitude * Math.PI * 2) * 0.3 + 0.7;
        cloudDensity *= seasonalEffect;

        // Diurnal cycle (simplified)
        const diurnalCycle = Math.sin(this.weatherTime * 0.1) * 0.2 + 0.8;
        cloudDensity *= diurnalCycle;

        // Atmospheric pressure effects
        const pressureEffect = Math.min(1.5, this.atmPressure);
        cloudDensity *= pressureEffect;

        // Temperature effects on cloud formation
        const tempEffect = Math.max(0.3, 1 - Math.abs(this.globalTemp - 288) / 100);
        cloudDensity *= tempEffect;

        return Math.max(0, Math.min(1, cloudDensity));
      }

      calculateCloudColor(nx, ny, nz, density, params) {
        // Calculate lighting effects on clouds
        const sunDirection = new THREE.Vector3(0.6, 0.4, 0.3).normalize();
        const normal = new THREE.Vector3(nx, ny, nz);
        const sunDot = normal.dot(sunDirection);

        // Base cloud color
        let brightness = params.brightness;

        // Lighting effects
        const lighting = Math.max(0.3, sunDot * 0.7 + 0.3);
        brightness *= lighting;

        // Density-based shading
        const densityShading = Math.max(0.4, 1 - density * 0.6);
        brightness *= densityShading;

        // Add slight color variation
        const colorVariation = EnhancedNoise.perlin3D(nx * 25, ny * 25, nz * 25, this.seed + 1600) * 0.1;
        brightness += colorVariation;

        // Clamp brightness
        brightness = Math.max(0, Math.min(1, brightness));

        // Convert to RGB
        const baseColor = 255 * brightness;

        return {
          r: Math.floor(baseColor),
          g: Math.floor(baseColor),
          b: Math.floor(baseColor * 1.05) // Slightly blue tint
        };
      }

      generateCloudShadowTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 1024;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        const imageData = ctx.createImageData(canvas.width, canvas.height);
        const data = imageData.data;

        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const u = x / canvas.width;
            const v = y / canvas.height;

            const theta = u * Math.PI * 2;
            const phi = v * Math.PI;

            const nx = Math.sin(phi) * Math.cos(theta);
            const ny = Math.cos(phi);
            const nz = Math.sin(phi) * Math.sin(theta);

            // Generate shadow pattern based on cloud density
            const shadowDensity = this.calculateAdvancedCloudDensity(nx, ny, nz, this.getCloudTypeParameters('low'));

            const index = (y * canvas.width + x) * 4;
            const shadowStrength = Math.min(255, shadowDensity * 128);

            data[index] = 0;     // R
            data[index + 1] = 0; // G
            data[index + 2] = 0; // B
            data[index + 3] = shadowStrength; // A
          }
        }

        ctx.putImageData(imageData, 0, 0);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        return texture;
      }

      initializeControls() {
        // Tectonic controls
        document.getElementById('tectonic-activity').addEventListener('input', async (e) => {
          if (this.isGenerating) return;
          this.tectonicActivity = parseFloat(e.target.value);
          document.getElementById('tectonic-value').textContent = e.target.value;
          await this.regeneratePlanet();
        });

        document.getElementById('mountain-formation').addEventListener('input', async (e) => {
          if (this.isGenerating) return;
          this.mountainFormation = parseFloat(e.target.value);
          document.getElementById('mountain-value').textContent = e.target.value;
          await this.regeneratePlanet();
        });

        document.getElementById('volcanic-activity').addEventListener('input', async (e) => {
          if (this.isGenerating) return;
          this.volcanicActivity = parseFloat(e.target.value);
          document.getElementById('volcanic-value').textContent = e.target.value;
          await this.regeneratePlanet();
        });

        // Climate controls
        document.getElementById('global-temp').addEventListener('input', (e) => {
          this.globalTemp = parseFloat(e.target.value);
          document.getElementById('temp-value').textContent = e.target.value + 'K';
          this.regeneratePlanet();
        });

        document.getElementById('precipitation').addEventListener('input', (e) => {
          this.precipitation = parseFloat(e.target.value);
          document.getElementById('precip-value').textContent = e.target.value;
          this.regeneratePlanet();
        });

        document.getElementById('ocean-coverage').addEventListener('input', (e) => {
          this.oceanCoverage = parseFloat(e.target.value);
          document.getElementById('ocean-value').textContent = Math.floor(e.target.value * 100) + '%';
          this.regeneratePlanet();
        });

        // Atmospheric controls
        document.getElementById('atm-pressure').addEventListener('input', (e) => {
          this.atmPressure = parseFloat(e.target.value);
          document.getElementById('pressure-value').textContent = e.target.value;
          this.updateAtmosphere();
        });

        document.getElementById('cloud-coverage').addEventListener('input', (e) => {
          this.cloudCoverage = parseFloat(e.target.value);
          document.getElementById('cloud-value').textContent = Math.floor(e.target.value * 100) + '%';
          this.updateClouds();
        });

        document.getElementById('seasonal-cycle').addEventListener('input', (e) => {
          this.seasonalCycle = parseFloat(e.target.value);
          const seasons = ['Spring', 'Summer', 'Autumn', 'Winter'];
          const seasonIndex = Math.floor(e.target.value * 4) % 4;
          document.getElementById('season-value').textContent = seasons[seasonIndex];
          this.updateClouds();
        });

        document.getElementById('atmospheric-scattering').addEventListener('input', (e) => {
          this.atmosphericScattering = parseFloat(e.target.value);
          document.getElementById('scattering-value').textContent = e.target.value + 'x';
          this.updateAtmosphere();
        });

        document.getElementById('cloud-dynamics').addEventListener('input', (e) => {
          this.cloudDynamics = parseFloat(e.target.value);
          document.getElementById('dynamics-value').textContent = e.target.value + 'x';
          this.updateClouds();
        });

        // Seed controls
        document.getElementById('planet-seed').addEventListener('input', async (e) => {
          if (this.isGenerating) return;
          const seedValue = e.target.value;
          this.seed = this.hashSeed(seedValue);
          await this.regeneratePlanet();
        });

        document.getElementById('random-seed').addEventListener('click', async () => {
          if (this.isGenerating) return;
          const randomSeed = Math.floor(Math.random() * 1000000);
          document.getElementById('planet-seed').value = randomSeed.toString();
          this.seed = randomSeed;
          await this.regeneratePlanet();
        });

        // Terrain controls
        document.getElementById('terrain-detail').addEventListener('input', (e) => {
          this.terrainDetail = parseInt(e.target.value);
          const levels = ['Low', 'Medium', 'High', 'Ultra', 'Extreme'];
          document.getElementById('detail-level').textContent = levels[e.target.value - 4] || 'Maximum';
          this.regeneratePlanet();
        });

        document.getElementById('lod-max-detail').addEventListener('input', (e) => {
          this.lodMaxDetail = parseInt(e.target.value);
          document.getElementById('lod-max-value').textContent = e.target.value;
          if (this.dynamicLOD) {
            this.regeneratePlanet();
          }
        });

        document.getElementById('lod-distance').addEventListener('input', (e) => {
          this.lodDistance = parseFloat(e.target.value);
          document.getElementById('lod-distance-value').textContent = e.target.value;
          this.updateLOD();
        });

        document.getElementById('surface-detail').addEventListener('input', (e) => {
          this.surfaceDetail = parseFloat(e.target.value);
          document.getElementById('surface-detail-value').textContent = e.target.value + 'x';
          if (this.dynamicLOD) {
            this.regeneratePlanet();
          }
        });

        document.getElementById('erosion-rate').addEventListener('input', (e) => {
          this.erosionRate = parseFloat(e.target.value);
          document.getElementById('erosion-value').textContent = e.target.value;
          this.regeneratePlanet();
        });

        // Feature toggles
        document.getElementById('toggle-boundaries').addEventListener('click', () => {
          this.features.boundaries = !this.features.boundaries;
          this.updateToggleButton('toggle-boundaries', this.features.boundaries);
        });

        document.getElementById('toggle-hotspots').addEventListener('click', () => {
          this.features.hotspots = !this.features.hotspots;
          this.updateToggleButton('toggle-hotspots', this.features.hotspots);
        });

        document.getElementById('toggle-atmosphere').addEventListener('click', () => {
          this.features.atmosphere = !this.features.atmosphere;
          this.updateToggleButton('toggle-atmosphere', this.features.atmosphere);
          this.updateAtmosphere();
        });

        document.getElementById('toggle-clouds').addEventListener('click', () => {
          this.features.clouds = !this.features.clouds;
          this.updateToggleButton('toggle-clouds', this.features.clouds);
          this.updateClouds();
        });

        document.getElementById('toggle-volumetric').addEventListener('click', () => {
          this.features.volumetric = !this.features.volumetric;
          this.updateToggleButton('toggle-volumetric', this.features.volumetric);
          this.updateVolumetricFeatures();
        });

        document.getElementById('toggle-shadows').addEventListener('click', () => {
          this.features.shadows = !this.features.shadows;
          this.updateToggleButton('toggle-shadows', this.features.shadows);
          this.updateShadowFeatures();
        });

        // Environmental controls
        document.getElementById('ocean-currents').addEventListener('input', (e) => {
          this.oceanCurrents = parseFloat(e.target.value);
          document.getElementById('currents-value').textContent = e.target.value + 'x';
        });

        document.getElementById('lightning-activity').addEventListener('input', (e) => {
          this.lightningActivity = parseFloat(e.target.value);
          document.getElementById('lightning-value').textContent = e.target.value + 'x';
        });

        document.getElementById('particle-density').addEventListener('input', (e) => {
          this.particleDensity = parseFloat(e.target.value);
          document.getElementById('particle-value').textContent = e.target.value + 'x';
          this.updateEnvironmentalParticles();
        });

        document.getElementById('toggle-lightning').addEventListener('click', () => {
          this.features.lightning = !this.features.lightning;
          this.updateToggleButton('toggle-lightning', this.features.lightning);
          if (this.features.lightning) {
            this.createLightningSystem();
          }
        });

        document.getElementById('toggle-particles').addEventListener('click', () => {
          this.features.particles = !this.features.particles;
          this.updateToggleButton('toggle-particles', this.features.particles);
          this.updateEnvironmentalParticles();
        });

        document.getElementById('toggle-currents').addEventListener('click', () => {
          this.features.currents = !this.features.currents;
          this.updateToggleButton('toggle-currents', this.features.currents);
        });

        document.getElementById('toggle-effects').addEventListener('click', () => {
          this.features.effects = !this.features.effects;
          this.updateToggleButton('toggle-effects', this.features.effects);
        });

        document.getElementById('toggle-wireframe').addEventListener('click', () => {
          this.wireframeMode = !this.wireframeMode;
          this.updateToggleButton('toggle-wireframe', this.wireframeMode);
          this.updateWireframeMode();
        });

        document.getElementById('toggle-lod').addEventListener('click', () => {
          this.dynamicLOD = !this.dynamicLOD;
          this.updateToggleButton('toggle-lod', this.dynamicLOD);
          this.regeneratePlanet();
        });

        document.getElementById('toggle-adaptive').addEventListener('click', () => {
          this.adaptiveSubdivision = !this.adaptiveSubdivision;
          this.updateToggleButton('toggle-adaptive', this.adaptiveSubdivision);
          if (this.dynamicLOD) {
            this.regeneratePlanet();
          }
        });

        document.getElementById('regenerate').addEventListener('click', async () => {
          if (this.isGenerating) return;
          const randomSeed = Math.floor(Math.random() * 1000000);
          document.getElementById('planet-seed').value = randomSeed.toString();
          this.seed = randomSeed;
          await this.regeneratePlanet();
        });

        // Quick action controls
        if (document.getElementById('quick-regenerate')) {
          document.getElementById('quick-regenerate').addEventListener('click', () => {
            this.regeneratePlanet();
          });
        }

        if (document.getElementById('quick-random')) {
          document.getElementById('quick-random').addEventListener('click', () => {
            this.randomizePlanet();
          });
        }

        // Generation control handlers
        if (document.getElementById('pause-generation-btn')) {
          document.getElementById('pause-generation-btn').addEventListener('click', () => {
            this.togglePauseGeneration();
          });
        }

        if (document.getElementById('cancel-generation-btn')) {
          document.getElementById('cancel-generation-btn').addEventListener('click', () => {
            this.cancelGeneration();
          });
        }

        // Advanced controls
        document.getElementById('geological-time').addEventListener('input', async (e) => {
          if (this.isGenerating) return;
          this.geologicalTime = parseFloat(e.target.value);
          document.getElementById('geological-value').textContent = e.target.value + ' Myr';
          await this.regeneratePlanet();
        });

        document.getElementById('magnetic-field').addEventListener('input', (e) => {
          this.magneticField = parseFloat(e.target.value);
          document.getElementById('magnetic-value').textContent = e.target.value + 'x';
          this.updateAurora();
        });

        document.getElementById('rotation-speed').addEventListener('input', (e) => {
          this.rotationSpeed = parseFloat(e.target.value);
          document.getElementById('rotation-value').textContent = e.target.value + 'x';
        });

        document.getElementById('toggle-aurora').addEventListener('click', () => {
          this.features.aurora = !this.features.aurora;
          this.updateToggleButton('toggle-aurora', this.features.aurora);
          this.updateAurora();
        });

        document.getElementById('toggle-rings').addEventListener('click', () => {
          this.features.rings = !this.features.rings;
          this.updateToggleButton('toggle-rings', this.features.rings);
          this.updateRings();
        });

        document.getElementById('toggle-magnetic-field').addEventListener('click', () => {
          this.features.magneticField = !this.features.magneticField;
          this.updateToggleButton('toggle-magnetic-field', this.features.magneticField);
          this.updateMagneticField();
        });

        document.getElementById('auto-rotate').addEventListener('click', () => {
          this.autoRotate = !this.autoRotate;
          this.updateToggleButton('auto-rotate', this.autoRotate);
        });

        // Layer controls
        ['surface', 'elevation', 'temperature', 'biomes', 'tectonics', 'weather'].forEach(layer => {
          const button = document.getElementById(`layer-${layer}`);
          if (button) {
            button.addEventListener('click', () => {
              this.setLayer(layer);
            });
          }
        });
      }

      updateToggleButton(buttonId, isActive) {
        const button = document.getElementById(buttonId);
        if (button) {
          button.className = `feature-toggle ${isActive ? 'active' : 'inactive'}`;
        }
      }

      setLayer(layer) {
        this.currentLayer = layer;

        // Update button states
        ['surface', 'elevation', 'temperature', 'biomes', 'tectonics', 'weather'].forEach(l => {
          const button = document.getElementById(`layer-${l}`);
          if (button) {
            button.className = `feature-toggle ${l === layer ? 'active' : 'inactive'}`;
          }
        });

        // Update visualization based on layer
        this.updateLayerVisualization();
      }

      updateLayerVisualization() {
        if (!this.planetMesh) return;

        const geometry = this.planetMesh.geometry;
        const positions = geometry.attributes.position;
        const colors = new Float32Array(positions.count * 3);
        const vertex = new THREE.Vector3();

        for (let i = 0; i < positions.count; i++) {
          vertex.fromBufferAttribute(positions, i);
          const normal = vertex.clone().normalize();
          const elevation = vertex.length() - 1.0;

          let color = { r: 0.5, g: 0.5, b: 0.5 };

          switch (this.currentLayer) {
            case 'elevation':
              const elevColor = (elevation + 0.2) / 0.4;
              color = {
                r: Math.max(0, Math.min(1, elevColor)),
                g: Math.max(0, Math.min(1, 1 - Math.abs(elevColor - 0.5) * 2)),
                b: Math.max(0, Math.min(1, 1 - elevColor))
              };
              break;

            case 'temperature':
              const latitude = Math.abs(Math.asin(normal.y));
              const temp = (1 - latitude) * 0.8 + 0.2;
              color = {
                r: temp,
                g: temp * 0.5,
                b: 1 - temp
              };
              break;

            case 'tectonics':
              const plateNoise = EnhancedNoise.worley(normal.x * 3, normal.y * 3, normal.z * 3, this.seed);
              if (plateNoise < 0.3) {
                color = { r: 1.0, g: 0.3, b: 0.1 }; // Plate boundaries
              } else {
                color = { r: 0.3, g: 0.3, b: 0.3 }; // Plate interiors
              }
              break;

            case 'weather':
              const weatherInfluence = this.getWeatherInfluence(normal);

              if (weatherInfluence.dominantSystem) {
                const system = weatherInfluence.dominantSystem;

                if (system.type === 'cyclone') {
                  // Cyclones in red/orange
                  const intensity = weatherInfluence.maxInfluence;
                  color = {
                    r: 0.8 + intensity * 0.2,
                    g: 0.2 + intensity * 0.3,
                    b: 0.1
                  };
                } else {
                  // Anticyclones in blue
                  const intensity = weatherInfluence.maxInfluence;
                  color = {
                    r: 0.1,
                    g: 0.3 + intensity * 0.2,
                    b: 0.7 + intensity * 0.3
                  };
                }
              } else {
                // Base cloud patterns
                const cloudNoise = EnhancedNoise.fractal(normal.x * 4, normal.y * 4, normal.z * 4, 3, 0.5, this.seed + 400);
                const cloudDensity = Math.max(0, cloudNoise - 0.2);
                color = {
                  r: 0.3 + cloudDensity * 0.4,
                  g: 0.3 + cloudDensity * 0.4,
                  b: 0.6 + cloudDensity * 0.4
                };
              }
              break;

            default: // surface or biomes
              color = this.getBiomeColor(elevation, this.globalTemp, this.precipitation, normal);
              break;
          }

          colors[i * 3] = color.r;
          colors[i * 3 + 1] = color.g;
          colors[i * 3 + 2] = color.b;
        }

        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.attributes.color.needsUpdate = true;
      }

      async regeneratePlanet() {
        if (this.isGenerating) return;

        // Check what needs to be regenerated
        this.checkRegenerationNeeds();

        // Use selective regeneration if possible
        if (this.canUseSelectiveRegeneration()) {
          await this.selectiveRegeneration();
        } else {
          await this.generatePlanet();
        }
      }

      checkRegenerationNeeds() {
        const current = {
          seed: this.seed,
          subdivisions: this.subdivisions,
          tectonicActivity: this.tectonicActivity,
          mountainFormation: this.mountainFormation,
          volcanicActivity: this.volcanicActivity,
          erosionRate: this.erosionRate,
          globalTemp: this.globalTemp,
          atmPressure: this.atmPressure,
          cloudCoverage: this.cloudCoverage,
          precipitation: this.precipitation,
          geologicalTime: this.geologicalTime,
          magneticField: this.magneticField,
          atmosphericScattering: this.atmosphericScattering,
          cloudDynamics: this.cloudDynamics
        };

        const last = this.lastParameters;

        // Reset flags
        Object.keys(this.regenerationFlags).forEach(key => {
          this.regenerationFlags[key] = false;
        });

        // Check geometry changes
        if (last.seed !== current.seed || last.subdivisions !== current.subdivisions) {
          this.regenerationFlags.geometry = true;
          this.regenerationFlags.tectonics = true;
          this.regenerationFlags.climate = true;
          this.regenerationFlags.atmosphere = true;
          this.regenerationFlags.clouds = true;
          this.regenerationFlags.weather = true;
          this.regenerationFlags.features = true;
        }

        // Check tectonic changes
        else if (last.tectonicActivity !== current.tectonicActivity ||
                 last.mountainFormation !== current.mountainFormation ||
                 last.volcanicActivity !== current.volcanicActivity ||
                 last.erosionRate !== current.erosionRate ||
                 last.geologicalTime !== current.geologicalTime) {
          this.regenerationFlags.tectonics = true;
          this.regenerationFlags.climate = true;
          this.regenerationFlags.features = true;
        }

        // Check atmospheric changes
        else if (last.globalTemp !== current.globalTemp ||
                 last.atmPressure !== current.atmPressure ||
                 last.atmosphericScattering !== current.atmosphericScattering) {
          this.regenerationFlags.atmosphere = true;
          this.regenerationFlags.climate = true;
        }

        // Check cloud changes
        else if (last.cloudCoverage !== current.cloudCoverage ||
                 last.precipitation !== current.precipitation ||
                 last.cloudDynamics !== current.cloudDynamics) {
          this.regenerationFlags.clouds = true;
          this.regenerationFlags.weather = true;
        }

        // Check magnetic field changes
        else if (last.magneticField !== current.magneticField) {
          this.regenerationFlags.features = true;
        }

        // Store current parameters
        this.lastParameters = { ...current };
      }

      canUseSelectiveRegeneration() {
        // Can use selective regeneration if geometry doesn't need to change
        return !this.regenerationFlags.geometry && this.geometryCache;
      }

      async selectiveRegeneration() {
        this.isGenerating = true;
        this.generationCancelled = false;
        this.generationStartTime = performance.now();

        try {
          // Use cached geometry
          let geometry = this.geometryCache.clone();

          // Apply only necessary updates
          if (this.regenerationFlags.tectonics) {
            this.updateProgress('Updating Tectonics', 'Applying tectonic changes...', 'tectonics');
            this.applyTectonicProcesses(geometry);
            await this.delay(100);
          }

          if (this.regenerationFlags.climate) {
            this.updateProgress('Updating Climate', 'Recalculating climate effects...', 'climate');
            this.applyClimateEffects(geometry);
            await this.delay(100);
          }

          // Update planet mesh
          if (this.regenerationFlags.tectonics || this.regenerationFlags.climate) {
            this.updateProgress('Updating Planet Surface', 'Applying surface changes...', 'surface');

            if (this.planetMesh) {
              this.planetMesh.geometry.dispose();
              this.planetMesh.geometry = geometry;
            } else {
              const material = this.createPlanetMaterial();
              this.planetMesh = new THREE.Mesh(geometry, material);
              this.planetMesh.castShadow = true;
              this.planetMesh.receiveShadow = true;
              this.scene.add(this.planetMesh);
            }
            await this.delay(100);
          }

          // Update atmospheric systems
          if (this.regenerationFlags.atmosphere) {
            this.updateProgress('Updating Atmosphere', 'Recalculating atmospheric effects...', 'atmosphere');
            this.updateAtmosphere();
            await this.delay(100);
          }

          // Update cloud systems
          if (this.regenerationFlags.clouds) {
            this.updateProgress('Updating Clouds', 'Regenerating cloud systems...', 'clouds');
            this.updateClouds();
            await this.delay(100);
          }

          // Update weather systems
          if (this.regenerationFlags.weather) {
            this.updateProgress('Updating Weather', 'Recalculating weather patterns...', 'weather');
            this.generateWeatherSystems();
            await this.delay(100);
          }

          // Update advanced features
          if (this.regenerationFlags.features) {
            this.updateProgress('Updating Features', 'Updating advanced features...', 'features');
            this.updateAdvancedFeatures();
            await this.delay(100);
          }

          // Update statistics
          this.updateProgress('Finalizing Updates', 'Completing selective regeneration...', 'finalize');
          this.updateStats();
          this.calculatePlanetAnalysis();
          await this.delay(200);

          const duration = (performance.now() - this.generationStartTime) / 1000;
          this.updateProgress(
            'Selective Regeneration Complete!',
            `Updated in ${duration.toFixed(1)}s using selective regeneration`,
            'complete'
          );

        } catch (error) {
          if (error.message === 'Generation cancelled') {
            this.updateProgress('Regeneration Cancelled', 'Selective regeneration was cancelled', 'cancelled');
          } else {
            console.error('Selective regeneration error:', error);
            this.updateProgress('Regeneration Error', 'Error during selective regeneration, falling back to full generation', 'error');
            await this.generatePlanet(); // Fallback to full generation
          }
        } finally {
          this.isGenerating = false;
        }
      }

      updateAdvancedFeatures() {
        // Update magnetic field
        this.updateMagneticField();

        // Update aurora
        this.updateAurora();

        // Update rings
        this.updateRings();

        // Update volumetric features
        this.updateVolumetricFeatures();

        // Update shadow features
        this.updateShadowFeatures();
      }

      updateWireframeMode() {
        // Update wireframe for all LOD meshes
        if (this.lodMeshes && this.lodMeshes.length > 0) {
          this.lodMeshes.forEach(mesh => {
            if (mesh && mesh.material) {
              mesh.material.wireframe = this.wireframeMode;
            }
          });
        } else if (this.planetMesh && this.planetMesh.material) {
          this.planetMesh.material.wireframe = this.wireframeMode;
        }
      }

      updateEnvironmentalParticles() {
        // Remove existing particles
        if (this.environmentalParticles) {
          this.scene.remove(this.environmentalParticles);
          if (this.environmentalParticles.geometry) this.environmentalParticles.geometry.dispose();
          if (this.environmentalParticles.material) this.environmentalParticles.material.dispose();
          this.environmentalParticles = null;
        }

        // Create new particles if enabled
        if (this.features.particles && this.particleDensity > 0.1) {
          this.createEnvironmentalParticleSystem();
        }
      }

      updateAtmosphere() {
        const shouldShowAtmosphere = this.features.atmosphere && this.atmPressure > 0.1;

        // Update main atmospheric components
        [this.atmosphereMesh, this.atmosphereGlow, this.stratosphereMesh,
         this.mesosphereMesh, this.volumetricMesh, this.scatteringMesh].forEach(mesh => {
          if (mesh) {
            mesh.visible = shouldShowAtmosphere;
          }
        });

        // Update atmospheric particles
        if (this.atmosphericParticles) {
          this.atmosphericParticles.visible = shouldShowAtmosphere && this.atmPressure > 0.3;
          if (this.atmosphericParticles.visible) {
            this.atmosphericParticles.material.opacity = this.atmPressure * 0.4;
          }
        }

        // Update shader uniforms
        if (this.atmosphereMesh && this.atmosphereMesh.material.uniforms) {
          this.atmosphereMesh.material.uniforms.pressure.value = this.atmPressure;
          this.atmosphereMesh.material.uniforms.scatteringStrength.value = this.atmPressure * 0.5;
        }

        // Create atmosphere if it doesn't exist
        if (!this.atmosphereMesh && shouldShowAtmosphere) {
          this.createAtmosphere();
        }
      }

      updateClouds() {
        const shouldShowClouds = this.features.clouds && this.cloudCoverage > 0.1;

        // Update all cloud layers
        const cloudLayers = [
          { mesh: this.lowCloudMesh, opacity: this.cloudCoverage * 0.8 },
          { mesh: this.midCloudMesh, opacity: this.cloudCoverage * 0.5 },
          { mesh: this.highCloudMesh, opacity: this.cloudCoverage * 0.3 },
          { mesh: this.cloudShadowMesh, opacity: this.cloudCoverage * 0.3 }
        ];

        cloudLayers.forEach(layer => {
          if (layer.mesh) {
            layer.mesh.visible = shouldShowClouds;
            if (layer.mesh.visible) {
              layer.mesh.material.opacity = layer.opacity;
            }
          }
        });

        // Update volumetric clouds
        if (this.volumetricCloudMesh) {
          this.volumetricCloudMesh.visible = shouldShowClouds;
          if (this.volumetricCloudMesh.visible) {
            this.volumetricCloudMesh.material.opacity = this.cloudCoverage * 0.15;
          }
        }

        // Create clouds if they don't exist
        if (!this.lowCloudMesh && shouldShowClouds) {
          this.createClouds();
        }
      }

      updateAurora() {
        // Remove existing aurora
        if (this.auroraRings) {
          this.auroraRings.forEach(ring => {
            this.scene.remove(ring);
            if (ring.geometry) ring.geometry.dispose();
            if (ring.material) ring.material.dispose();
          });
          this.auroraRings = null;
        }

        // Create new aurora if enabled
        if (this.features.aurora && this.magneticField > 0.3) {
          this.createAurora();
        }
      }

      updateRings() {
        // Remove existing rings
        if (this.planetRings) {
          this.scene.remove(this.planetRings);
          if (this.planetRings.geometry) this.planetRings.geometry.dispose();
          if (this.planetRings.material) {
            if (this.planetRings.material.map) this.planetRings.material.map.dispose();
            this.planetRings.material.dispose();
          }
          this.planetRings = null;
        }

        // Create new rings if enabled
        if (this.features.rings) {
          this.createPlanetaryRings();
        }
      }

      updateStats() {
        if (this.planetMesh) {
          const geometry = this.planetMesh.geometry;
          document.getElementById('vertex-count').textContent = geometry.attributes.position.count.toLocaleString();
          document.getElementById('triangle-count').textContent = Math.floor(geometry.attributes.position.count / 3).toLocaleString();
        }

        // Update feature counts
        document.getElementById('plate-count').textContent = '7';
        document.getElementById('biome-count').textContent = '9';
        document.getElementById('weather-count').textContent = this.weatherSystems.length.toString();

        // Count active cyclones
        const activeCyclones = this.weatherSystems.filter(s => s.type === 'cyclone' && s.currentIntensity > 0.3).length;
        document.getElementById('cyclone-count').textContent = activeCyclones.toString();

        // Update weather info
        const seasons = ['Spring', 'Summer', 'Autumn', 'Winter'];
        const seasonIndex = Math.floor(this.seasonalCycle * 4) % 4;
        document.getElementById('current-season').textContent = seasons[seasonIndex];
        document.getElementById('weather-time').textContent = this.weatherTime.toFixed(1) + 's';

        // Calculate storm activity
        const avgIntensity = this.weatherSystems.reduce((sum, s) => sum + (s.currentIntensity || 0), 0) / this.weatherSystems.length;
        const stormLevel = avgIntensity < 0.3 ? 'Low' : avgIntensity < 0.7 ? 'Moderate' : 'High';
        document.getElementById('storm-activity').textContent = stormLevel;

        document.getElementById('current-clouds').textContent = Math.floor(this.cloudCoverage * 100) + '%';

        // Update planet analysis
        this.calculatePlanetAnalysis();
        this.calculateAtmosphericComposition();
      }

      onWindowResize() {
        const canvas = document.getElementById('planet-canvas');
        this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
      }

      animate() {
        this.animationId = requestAnimationFrame(() => this.animate());

        // Calculate delta time and performance metrics
        const now = performance.now();
        if (!this.lastFrameTime) this.lastFrameTime = now;
        const deltaTime = (now - this.lastFrameTime) * 0.001;
        const frameTime = now - this.lastFrameTime;

        // Update weather time
        this.weatherTime += deltaTime;

        // Update weather systems
        this.updateWeatherSystems(deltaTime);

        // Rotate planet with configurable speed (if auto-rotate is enabled)
        if (this.autoRotate) {
          const rotationSpeed = this.rotationSpeed * 0.003;
          if (this.planetMesh) {
            this.planetMesh.rotation.y += rotationSpeed;
          }

          // Animate cloud layers at different speeds
          if (this.lowCloudMesh) {
            this.lowCloudMesh.rotation.y += rotationSpeed * 1.2;
          }
          if (this.midCloudMesh) {
            this.midCloudMesh.rotation.y += rotationSpeed * 1.4;
          }
          if (this.highCloudMesh) {
            this.highCloudMesh.rotation.y += rotationSpeed * 1.6;
          }
          if (this.cloudShadowMesh) {
            this.cloudShadowMesh.rotation.y += rotationSpeed * 1.2;
          }
        }

        // Animate atmospheric particles
        if (this.atmosphericParticles && this.autoRotate) {
          const rotationSpeed = this.rotationSpeed * 0.003;
          this.atmosphericParticles.rotation.y += rotationSpeed * 0.3;
          this.atmosphericParticles.rotation.x += rotationSpeed * 0.15;
        }

        // Animate atmospheric layers
        const time = now * 0.001;

        if (this.atmosphereMesh && this.atmosphereMesh.material.uniforms) {
          this.atmosphereMesh.material.uniforms.time.value = time;
          this.atmosphereMesh.material.uniforms.pressure.value = this.atmPressure;
        }

        if (this.atmosphereGlow) {
          const pulse = Math.sin(time * 0.5) * 0.1 + 0.9;
          this.atmosphereGlow.material.opacity = this.atmPressure * 0.15 * pulse;
        }

        // Animate atmospheric layers
        if (this.stratosphereMesh) {
          this.stratosphereMesh.rotation.y += 0.0005;
          const stratoPulse = Math.sin(time * 0.3) * 0.05 + 0.95;
          this.stratosphereMesh.material.opacity = this.atmPressure * 0.1 * stratoPulse;
        }

        if (this.mesosphereMesh) {
          this.mesosphereMesh.rotation.y += 0.0003;
          const mesoPulse = Math.sin(time * 0.2) * 0.03 + 0.97;
          this.mesosphereMesh.material.opacity = this.atmPressure * 0.05 * mesoPulse;
        }

        // Animate volumetric atmosphere
        if (this.volumetricMesh) {
          this.volumetricMesh.rotation.y += 0.0008;
          this.volumetricMesh.rotation.x += 0.0004;
        }

        // Animate scattering layer
        if (this.scatteringMesh) {
          this.scatteringMesh.rotation.y += 0.0002;
          const scatterPulse = Math.sin(time * 0.4) * 0.02 + 0.98;
          this.scatteringMesh.material.opacity = this.atmPressure * 0.08 * scatterPulse;
        }

        // Animate volumetric clouds
        if (this.volumetricCloudMesh && this.autoRotate) {
          const rotationSpeed = this.rotationSpeed * 0.003;
          this.volumetricCloudMesh.rotation.y += rotationSpeed * 0.8;

          // Update volumetric cloud positions periodically
          if (this.frameCount % 120 === 0) {
            this.updateVolumetricCloudPositions();
          }
        }

        // Animate aurora
        if (this.auroraRings) {
          const time = now * 0.001;
          this.auroraRings.forEach((ring, index) => {
            ring.rotation.z += (0.001 + index * 0.0005) * this.magneticField;

            // Animate aurora colors
            const hue = (time * 0.1 + index * 0.3) % 1;
            ring.material.color.setHSL(hue, 0.8, 0.6);

            // Animate aurora intensity
            const intensity = Math.sin(time * 2 + index) * 0.2 + 0.8;
            ring.material.opacity = this.magneticField * 0.4 * intensity;
          });
        }

        // Animate planetary rings
        if (this.planetRings && this.autoRotate) {
          const rotationSpeed = this.rotationSpeed * 0.003;
          this.planetRings.rotation.z += rotationSpeed * 0.1;
        }

        // Update performance stats
        const fps = Math.round(1000 / frameTime);

        if (!this.frameCount) this.frameCount = 0;
        this.frameCount++;

        // Update performance tracking every frame
        this.updatePerformanceStats(fps, frameTime);

        // Update detailed stats every 30 frames for performance
        if (this.frameCount % 30 === 0) {
          this.updateStats();
        }

        // Update LOD system every 10 frames
        if (this.frameCount % 10 === 0) {
          this.updateLOD();
        }

        // Update environmental systems
        this.updateLightningSystem(deltaTime);
        this.updateEnvironmentalParticles(deltaTime);

        this.lastFrameTime = now;

        this.renderer.render(this.scene, this.camera);
      }

      randomizePlanet() {
        if (this.isGenerating) return;

        // Randomize all parameters
        this.seed = Math.floor(Math.random() * 1000000);
        this.globalTemp = 200 + Math.random() * 200;
        this.atmPressure = Math.random() * 3;
        this.tectonicActivity = Math.random() * 2;
        this.mountainFormation = Math.random() * 2;
        this.volcanicActivity = Math.random() * 2;
        this.cloudCoverage = Math.random();
        this.precipitation = Math.random() * 2;
        this.oceanCoverage = Math.random();
        this.seasonalCycle = Math.random();

        // Update UI
        document.getElementById('planet-seed').value = this.seed.toString();
        document.getElementById('global-temp').value = this.globalTemp.toString();
        document.getElementById('atm-pressure').value = this.atmPressure.toString();
        document.getElementById('tectonic-activity').value = this.tectonicActivity.toString();
        document.getElementById('mountain-formation').value = this.mountainFormation.toString();
        document.getElementById('volcanic-activity').value = this.volcanicActivity.toString();
        document.getElementById('cloud-coverage').value = this.cloudCoverage.toString();
        document.getElementById('precipitation').value = this.precipitation.toString();
        document.getElementById('ocean-coverage').value = this.oceanCoverage.toString();
        document.getElementById('seasonal-cycle').value = this.seasonalCycle.toString();

        // Update value displays
        document.getElementById('temp-value').textContent = Math.round(this.globalTemp) + 'K';
        document.getElementById('pressure-value').textContent = this.atmPressure.toFixed(1) + 'x';
        document.getElementById('tectonic-value').textContent = this.tectonicActivity.toFixed(1) + 'x';
        document.getElementById('mountain-value').textContent = this.mountainFormation.toFixed(1) + 'x';
        document.getElementById('volcanic-value').textContent = this.volcanicActivity.toFixed(1) + 'x';
        document.getElementById('cloud-value').textContent = Math.round(this.cloudCoverage * 100) + '%';
        document.getElementById('precipitation-value').textContent = this.precipitation.toFixed(1) + 'x';
        document.getElementById('ocean-value').textContent = Math.round(this.oceanCoverage * 100) + '%';

        // Regenerate planet
        this.regeneratePlanet();
      }

      togglePauseGeneration() {
        if (!this.isGenerating) return;

        this.generationPaused = !this.generationPaused;

        const pauseBtn = document.getElementById('pause-generation-btn');
        if (pauseBtn) {
          pauseBtn.textContent = this.generationPaused ? '▶️ Resume' : '⏸️ Pause';
        }

        if (document.getElementById('progress-status')) {
          document.getElementById('progress-status').textContent = this.generationPaused ? 'Paused' : 'Running';
        }
      }

      cancelGeneration() {
        if (!this.isGenerating) return;

        this.generationCancelled = true;
        this.generationPaused = false;

        if (document.getElementById('progress-status')) {
          document.getElementById('progress-status').textContent = 'Cancelling...';
        }

        // Hide progress overlay after a short delay
        setTimeout(() => {
          this.hideProgress();
        }, 1000);
      }

      showProgress() {
        const progressOverlay = document.getElementById('progress-overlay');
        if (progressOverlay) {
          progressOverlay.style.display = 'flex';
        }

        this.generationStartTime = performance.now();
        this.currentStep = 1;

        if (document.getElementById('progress-status')) {
          document.getElementById('progress-status').textContent = 'Running';
        }

        // Reset pause button
        const pauseBtn = document.getElementById('pause-generation-btn');
        if (pauseBtn) {
          pauseBtn.textContent = '⏸️ Pause';
        }
      }

      hideProgress() {
        const progressOverlay = document.getElementById('progress-overlay');
        if (progressOverlay) {
          progressOverlay.style.display = 'none';
        }

        this.isGenerating = false;
        this.generationCancelled = false;
        this.generationPaused = false;
      }
    }

    // Initialize the enhanced planetary system
    window.addEventListener('load', () => {
      new EnhancedPlanetarySystem();
    });

    console.log('Enhanced Planetary System - Complete System Loaded');
  </script>
</body>
</html>
