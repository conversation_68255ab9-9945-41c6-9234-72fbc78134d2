<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Universe Model Demo</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .port {
      width: 1rem;
      height: 1rem;
    }
  </style>
</head>
<body class="bg-gray-50 text-gray-900 p-8 font-sans">
  <div class="bg-blue-600 text-white text-3xl font-semibold px-6 py-4 rounded-md shadow mb-8 inline-block">Universe Model Demo</div>
  <div id="output" class="space-y-4"></div>

  <script type="module">
    import { SeedManager } from '../dist/SeedManager.js';
    import { PropertyGraph } from '../dist/PropertyGraph.js';
    import { ProceduralEntity } from '../dist/ProceduralEntity.js';
    import { createPlanetDefinitions } from '../dist/PlanetDefinitions.js';

    const seedManager = new SeedManager('DemoSeed42');
    const graph = new PropertyGraph(createPlanetDefinitions());
    const planet = new ProceduralEntity('Planet-X', ['MilkyWay','System-4','Planet-X'], seedManager, graph);
console.log('Planet Entity:', planet);
    const groups = planet.generateGrouped();
    const trace = planet.generateTrace();
    const container = document.getElementById('output');
    const rowMap = new Map();

    for (const [group, props] of Object.entries(groups)) {
      const card = document.createElement('div');
      card.className = 'bg-white p-6 rounded-lg shadow space-y-2 w-72';

      const header = document.createElement('div');
      header.className = 'text-xl mb-2 underline';
      header.textContent = group;
      card.appendChild(header);

      for (const [key, value] of Object.entries(props)) {
        const row = document.createElement('div');
        row.className = 'bg-blue-50 hover:bg-blue-100 text-gray-800 px-4 py-2 rounded cursor-pointer overflow-hidden';

        const wrapper = document.createElement('div');
        wrapper.className = 'flex items-center justify-between space-x-2 w-full';

        const inPort = document.createElement('div');
        inPort.className = 'port bg-red-300 rounded-full border border-red-400';

        const keySpan = document.createElement('span');
        keySpan.className = 'font-mono truncate flex-1';
        keySpan.textContent = key + ':';

        const valueSpan = document.createElement('span');
        valueSpan.className = 'truncate';
        valueSpan.textContent = value;

        const outPort = document.createElement('div');
        outPort.className = 'port bg-blue-300 rounded-full border border-blue-400';

        wrapper.appendChild(inPort);
        wrapper.appendChild(keySpan);
        wrapper.appendChild(valueSpan);
        wrapper.appendChild(outPort);
        row.appendChild(wrapper);

        const inputs = trace[key]?.inputs || {};
        const inputStr = Object.entries(inputs).map(([k,v]) => `${k}: ${v}`).join(', ');
        const def = graph.getDefinition(key);
        const formula = def && def.compute ? def.compute.toString().replace(/\n/g, ' ') : '';
        row.title = formula + (inputStr ? ` | ${inputStr}` : '');
        row.addEventListener('click', () => {
          document.querySelectorAll('.highlight').forEach(el => el.classList.remove('ring-2','ring-yellow-400','highlight'));
          row.classList.add('ring-2','ring-yellow-400','highlight');
          const inputs = trace[key]?.inputs || {};
          Object.keys(inputs).forEach(inp => {
            const el = rowMap.get(inp);
            if (el) el.classList.add('ring-2','ring-yellow-400','highlight');
          });
        });
        rowMap.set(key, row);
        card.appendChild(row);
      }
      container.appendChild(card);
    }
  </script>
</body>
</html>
