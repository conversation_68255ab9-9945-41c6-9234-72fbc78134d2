<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Universe Model Visual – refactor build</title>

  <!-- 1 ▪ Styling (Tailwind) -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- 2 ▪ Runtime helpers that the refactor expects to find on window -->
  <script src="https://unpkg.com/leader-line@1.0.7/leader-line.min.js"></script>
  <script src="https://unpkg.com/interactjs/dist/interact.min.js"></script>

  <!-- 3 ▪ Your **domain** modules (Planet, PropertyGraph, etc.)  
          They can stay as ES-modules; just export them. -->
  <script type="module">
    import { SeedManager }           from "/dist/SeedManager.js";
    import { PropertyGraph }         from "/dist/PropertyGraph.js";
    import { ProceduralEntity }      from "/dist/ProceduralEntity.js";
    import { createPlanetDefinitions } from "/dist/PlanetDefinitions.js";

    /* Expose them globally because graphEditorRefactor.js references
       the names directly.  If you’d rather keep everything import-based,
       just add identical `import … from …` lines at the top of
       graphEditorRefactor.js instead and skip this block. */
    Object.assign(window, {
      SeedManager,
      PropertyGraph,
      ProceduralEntity,
      createPlanetDefinitions
    });
  </script>
  <!-- 4 ▪ The editor itself, which is now a refactored version -->
  <script type="module" src="graphEditorRefactor.js"></script>
</head>

<body class="bg-gray-50 text-gray-900">
  <!-- 4 ▪ Toolbar / workspace markup (unchanged) -->
  <div id="toolbar" class="absolute z-10 top-2 left-2 space-x-2">
    <label class="text-sm">Snap
      <input id="snapInput" type="number" value="20" min="1"
             class="ml-1 w-16 border border-gray-300 rounded px-1" />
    </label>
    <button id="resetBtn"
            class="bg-white border border-gray-300 px-2 py-1 rounded shadow text-sm hover:bg-gray-100">
      Reset view
    </button>
    <button id="clearBtn"
            class="bg-white border border-gray-300 px-2 py-1 rounded shadow text-sm hover:bg-gray-100">
      Clear connections
    </button>
  </div>

  <div id="workspace" class="bg-grid w-full h-screen relative overflow-hidden">
    <div id="canvas" class="absolute inset-0"></div>
  </div>
  <div id="contextMenu"
       class="absolute bg-white border border-gray-300 rounded shadow text-sm hidden z-20"></div>

  
</body>
</html>
